"""
Browser Agent - Preprocessor for single-page reconnaissance

This agent handles deep web reconnaissance, UI interaction,
and generates a structured JSON output for a single target webpage.
It uses an intelligent snapshot strategy to balance reliability and efficiency.
"""

import json
import asyncio
import time
import signal
import atexit
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
from contextlib import asynccontextmanager
from urllib.parse import urlparse, parse_qs

from agno.agent import Agent

# Note: agno.tools.mcp might not be available in current agno version
try:
    from agno.tools.mcp import MCPTools
except ImportError:
    # Use fallback from mcp_manager
    from shared.mcp_manager import MCPTools

import sys

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from shared.config import VAPTConfig, get_vapt_config
from shared.llm_factory import LLMFactory
from shared.logger import get_logger
from shared.mcp_manager import MCPManager

# Import LLM logging system
try:
    from LLM_logs import get_llm_logger
    LLM_LOGGING_AVAILABLE = True
except ImportError:
    LLM_LOGGING_AVAILABLE = False

class BrowserAgent:
    """
    Browser Agent for deep single-page reconnaissance and data collection.
    
    Uses playwright-mcp for browser automation and generates
    a comprehensive JSON report for the specified URL. It employs
    an intelligent snapshot strategy to optimize performance.
    """
    
    def __init__(self, config: Optional[VAPTConfig] = None):
        self.config = config or get_vapt_config()
        self.logger = get_logger("browser_agent", self.config.log_level)
        self.llm = LLMFactory.create_llm(self.config.llm)
        self.mcp_manager = MCPManager(self.config)
        self.target_url = ""  # Will be set during reconnaissance
        self.interaction_counter = 0
        self.browser_closed = False
        self.termination_requested = False
        self.current_agent = None  # Track current agent for cleanup

        # Initialize LLM logging if available
        self.llm_logger = None
        if LLM_LOGGING_AVAILABLE:
            try:
                self.llm_logger = get_llm_logger()
                self.logger.info("LLM logging initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize LLM logging: {e}")

        # Set up termination handlers
        self._setup_termination_handlers()

        # Report data structure focused on a single page
        self.report_data = {
            "metadata": {
                "target_url": None,
                "start_time": None,
                "end_time": None,
                "duration": None,
                "total_interactions": 0,
                "total_forms_submitted": 0,
                "total_scroll_actions": 0,
                "browser_closed": False,
                "llm_interactions": 0,
                "llm_cost_usd": 0.0
            },
            "recon": [],
            "network_logs": [],
            "console_logs": [],
            "raw_request": [],
            "component_interactions": [],
            "session_states": [],
            "detailed_logs": [],
            "scroll_interactions": [],
            "form_submissions": [],
            "llm_interactions": [],  # New section for LLM logs
            "application_analysis": {
                "detected_type": "unknown",
                "confidence": 0.0,
                "features": [],
                "technology_stack": [],
                "reasoning": ""
            }
        }

        self.logger.info("Browser Agent initialized for intelligent single-page deep analysis.")

    def _setup_termination_handlers(self):
        """Set up signal handlers for graceful termination"""
        def signal_handler(signum, frame):
            self.logger.warning(f"🛑 Received termination signal {signum}")
            self.termination_requested = True
            asyncio.create_task(self._handle_termination("signal_interrupt"))

        # Register signal handlers for common termination signals
        try:
            signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
            signal.signal(signal.SIGTERM, signal_handler)  # Termination request
            if hasattr(signal, 'SIGHUP'):  # Unix systems
                signal.signal(signal.SIGHUP, signal_handler)
        except (ValueError, OSError) as e:
            self.logger.warning(f"Could not set up all signal handlers: {e}")

        # Register cleanup function for normal exit
        atexit.register(self._cleanup_on_exit)

    def _cleanup_on_exit(self):
        """Cleanup function called on normal program exit"""
        if not self.browser_closed and not self.termination_requested:
            self.logger.info("🧹 Performing cleanup on exit")
            # Note: This runs in sync context, so we can't use async cleanup
            self.termination_requested = True

    async def _handle_termination(self, reason: str):
        """Handle termination request gracefully"""
        if self.termination_requested:
            self.logger.info(f"🛑 Handling termination: {reason}")

            # Try to close browser if we have an active agent
            if self.current_agent and not self.browser_closed:
                try:
                    self.logger.info("🔄 Attempting to close browser before termination...")
                    await asyncio.wait_for(
                        self._ensure_browser_cleanup(self.current_agent),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    self.logger.warning("⏰ Browser cleanup timed out during termination")
                except Exception as e:
                    self.logger.error(f"❌ Error during termination cleanup: {e}")

            # Save emergency report if we have data
            if self.target_url and self.report_data.get("recon"):
                await self._emergency_save_report(self.target_url, f"terminated_{reason}")

            self.logger.info("✅ Termination handling complete")
    
    @asynccontextmanager
    async def _get_mcp_tools(self):
        """Get MCP tools connection with comprehensive error handling"""
        self.logger.info("Connecting to MCP server...")
        try:
            async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
                self.logger.info("Connected to MCP server successfully")
                yield mcp_tools
        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server: {e}")
            self.logger.info("Make sure MCP server is running at: http://localhost:8931")
            self.logger.info("Start it with: node cli.js --browser chrome --port 8931")
            raise
    
    async def run_reconnaissance(self, target_url: str) -> str:
        """
        Run comprehensive reconnaissance on a single target URL.
        
        Args:
            target_url: Target URL to analyze
            
        Returns:
            Path to the generated JSON report
        """
        start_time = datetime.now()
        self.target_url = target_url
        self.logger.info(f"Starting deep analysis for single page: {target_url}")

        self.report_data["metadata"] = {
            "target_url": target_url,
            "start_time": start_time.isoformat(),
            "end_time": None,
            "duration": None
        }
        
        try:
            async with self._get_mcp_tools() as mcp_tools:
                agent = Agent(
                    name="Single-Page Browser Analyst",
                    role="You are a meticulous single-page analyst. Your function is to be the remote 'eyes and hands' inside a web browser, executing precise instructions to exhaustively interact with and analyze a single web page.",
                    goal=f"Given the target webpage {target_url}, browse, exhaustively interact with every element, and analyze its functionality.",
                    tools=[mcp_tools],
                    model=self.llm,
                    debug_mode=True,
                    show_tool_calls=True,
                    instructions=self._get_agent_instructions(),
                    memory=True
                )

                # Track current agent for termination handling
                self.current_agent = agent

                await self._perform_reconnaissance(agent, target_url)
                await asyncio.sleep(1) # Settle time

        except KeyboardInterrupt:
            self.logger.warning("🛑 Scan interrupted by user - saving partial results")
            self.termination_requested = True
            await self._handle_termination("keyboard_interrupt")
            raise
        except Exception as e:
            self.logger.log_agent_error("browser_agent", e, "reconnaissance")
            await self._emergency_save_report(target_url, f"error: {str(e)[:100]}")
            await asyncio.sleep(1)
            raise
        finally:
            # Clear current agent reference
            self.current_agent = None
        
        # Finalize report
        end_time = datetime.now()
        self.report_data["metadata"]["end_time"] = end_time.isoformat()
        self.report_data["metadata"]["duration"] = str(end_time - start_time)

        # Add final LLM interaction summary
        if self.llm_logger:
            try:
                summary = self.llm_logger.get_session_summary()
                self.report_data["metadata"]["llm_interactions"] = summary.get("total_interactions", 0)
                self.report_data["metadata"]["llm_cost_usd"] = summary.get("total_cost_usd", 0.0)
                self.report_data["metadata"]["llm_input_tokens"] = summary.get("total_input_tokens", 0)
                self.report_data["metadata"]["llm_output_tokens"] = summary.get("total_output_tokens", 0)

                self.logger.info(f"LLM Usage Summary: {summary.get('total_interactions', 0)} interactions, "
                               f"${summary.get('total_cost_usd', 0.0):.6f} cost, "
                               f"{summary.get('total_input_tokens', 0)} input tokens, "
                               f"{summary.get('total_output_tokens', 0)} output tokens")
            except Exception as e:
                self.logger.warning(f"Failed to get LLM session summary: {e}")

        # Save report
        report_path = self._save_report(target_url)
        self.logger.info(f"Analysis complete. Report saved to: {report_path}")

        return report_path

    async def _emergency_save_report(self, target_url: str, reason: str):
        """Save report on errors or interruption"""
        try:
            end_time = datetime.now()
            self.report_data["metadata"]["end_time"] = end_time.isoformat()
            self.report_data["metadata"]["status"] = "partial"
            self.report_data["metadata"]["termination_reason"] = reason
            report_path = self._save_report(target_url, suffix="_partial")
            self.logger.info(f"🚨 Emergency report saved to: {report_path}")
        except Exception as e:
            self.logger.error(f"Failed to save emergency report: {e}")

    def _save_report(self, target_url: str, suffix: str = "") -> str:
        """Save the final report to a JSON file."""
        try:
            domain = urlparse(target_url).netloc.replace(":", "_")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recon_report_{domain}_{timestamp}{suffix}.json"
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            report_path = reports_dir / filename
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.report_data, f, indent=2, ensure_ascii=False)
            return str(report_path)
        except Exception as e:
            self.logger.error(f"Error saving report: {e}")
            try:
                backup_path = f"emergency_report_{int(time.time())}.json"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(self.report_data, f, indent=2, ensure_ascii=False)
                return backup_path
            except:
                return "report_save_failed"

    def _get_agent_instructions(self) -> str:
        """
        Provides the Standard Operating Procedure (SOP) for the agent,
        including an intelligent strategy for using browser_snapshot.
        """
        return """
You are an expert security testing specialist with machine-like precision, focused on the deep analysis of a SINGLE web page. Your mission is to exhaustively interact with every element on a given page according to the protocol below.

*** CRITICAL BEHAVIORAL RULES ***
1.  **Tab Management**: If a click opens a new tab, you MUST immediately switch to it, check its purpose, close it, and return to the original tab. Your focus remains on the single page assigned.
2.  **Stay on Target**: Do NOT follow navigational links that lead to a different page. If you accidentally navigate away, use `browser_navigate_back()` immediately. Focus on on-page actions.
3.  **Mandatory Cleanup**: At the end of any analysis task, you MUST call `browser_close()` to terminate the browser session.

*** INTELLIGENT SNAPSHOT STRATEGY ***
The `browser_snapshot()` tool gives you a map of the page but uses resources. Use it strategically:
- **Take ONE baseline snapshot** right after the page loads for your initial analysis.
- **Take a NEW snapshot ONLY when the page's interactive layout changes significantly.** This includes:
  - After opening a modal, popup, or dialog.
  - After revealing new content by clicking "Load More" or a tab.
  - After a significant error appears on a form, changing the layout.
- Do NOT take snapshots after simple interactions like typing in a field or hovering.

*** SINGLE-PAGE DEEP ANALYSIS PROTOCOL ***
When given a URL to analyze, you will execute the following exhaustive protocol:

1.  **Initial Load & Analysis**:
    - Navigate to the provided URL.
    - **Take one baseline `browser_snapshot()` to get your initial map of the page.**
    - Collect initial data using `browser_network_requests()` and `browser_console_messages()`.
    - Briefly analyze the page's purpose.

2.  **Exhaustive & Strategic Interaction**:
    - **SCROLL**: Scroll to the top, middle, and bottom of the page to reveal all content.
    - **INTERACT WITH ELEMENTS**: Systematically go through all interactive elements (buttons, links, form fields, dropdowns, etc.) from your initial snapshot.
    - **MODALS & DYNAMIC CONTENT**: If clicking an element opens a modal or loads new content, **you MUST take a new `browser_snapshot()`** to see the new layout. Interact fully with this new content (e.g., fill the modal, then close it), and then continue your analysis of the main page, taking another snapshot if the main page has changed after the modal closes.
    - **FORMS**:
        - For each form, attempt to submit it empty to test validation.
        - Fill all fields with realistic test data (e.g., "<EMAIL>", "TestPassword123!") and submit it.
    - **OTHER INTERACTIONS**: Hover over elements, select all dropdown options, and check all checkboxes.

3.  **Continuous Data Collection**:
    - After EVERY significant interaction (form submission, modal-opening click), you MUST call `browser_network_requests()` and `browser_console_messages()` to capture the results.
    - When calling `browser_network_requests()`, request COMPLETE details including:
      * Full HTTP headers (request and response)
      * Request and response bodies
      * Status codes and timing information
      * Content-Type and other metadata
    - When calling `browser_console_messages()`, capture ALL console output including:
      * JavaScript errors with stack traces
      * Network errors and warnings
      * Debug messages and logs
      * Security-related console messages
    - Include the COMPLETE, raw output from these tools in your final report under "=== NETWORK REQUESTS ===" and "=== CONSOLE MESSAGES ===".
"""

    async def _perform_reconnaissance(self, agent: Agent, target_url: str):
        """Perform deep analysis on the single target page by issuing a direct command."""
        try:
            # Check for termination before starting
            if self.termination_requested:
                self.logger.info("🛑 Termination requested, skipping reconnaissance")
                return

            analysis_prompt = f"""
Your task is to conduct a deep analysis of the following webpage.
Apply your full SINGLE-PAGE DEEP ANALYSIS PROTOCOL, including your intelligent snapshot strategy.

Target URL: {target_url}

Begin your analysis now. Document every step, use snapshots wisely, and capture all data as per your protocol. When finished, ensure you call browser_close().
"""
            self.logger.info(f"Issuing deep analysis task order for: {target_url}")
            analysis_results = await self._execute_with_retry(agent, analysis_prompt)

            # Check for termination after analysis
            if self.termination_requested:
                self.logger.info("🛑 Termination requested during analysis")
                return

            self.report_data["recon"].append(analysis_results)
            self._parse_and_log_interactions(analysis_results, target_url)

            await self._collect_playwright_data(agent)
            await self._ensure_browser_cleanup(agent)

            self.report_data["metadata"]["total_interactions"] = self.interaction_counter
            self.report_data["metadata"]["total_forms_submitted"] = len(self.report_data.get("form_submissions", []))
            self.report_data["metadata"]["total_scroll_actions"] = len(self.report_data.get("scroll_interactions", []))
            self.report_data["metadata"]["browser_closed"] = self.browser_closed

            combined_content = "\n".join(self.report_data["recon"])
            app_analysis = self._analyze_application_type(combined_content, self.target_url)
            self.report_data["application_analysis"] = app_analysis

            self.logger.info(f"Exhaustive analysis completed. App type: {app_analysis['detected_type']}")

        except Exception as e:
            self.logger.log_agent_error("browser_agent", e, "analysis execution")
            try:
                await self._ensure_browser_cleanup(agent)
            except:
                pass
            raise

    def _parse_and_log_interactions(self, agent_result: str, url: str):
        """Parse the agent's text result to log interactions with improved pattern matching."""
        lines = agent_result.lower().split('\n')

        # Enhanced patterns for better interaction detection
        click_patterns = [
            "clicking", "clicked", "click", "button interaction", "interacting with",
            "pressing", "pressed", "selecting", "selected", "activating", "activated"
        ]

        form_patterns = [
            "submitting form", "form submission", "submit", "filling form", "form data",
            "testing forms", "contact form", "newsletter", "input field", "form validation"
        ]

        scroll_patterns = [
            "scrolling", "scroll", "scrolled", "scroll through", "scroll action",
            "phases: top, middle, and bottom", "revealing content"
        ]

        navigation_patterns = [
            "navigate", "navigating", "redirected", "redirect", "page to", "loading",
            "returned to", "back to", "browser_navigate"
        ]

        for line in lines:
            line_stripped = line.strip()
            if not line_stripped:
                continue

            ts = datetime.now().isoformat()

            # Check for click interactions
            if any(pattern in line for pattern in click_patterns):
                self.interaction_counter += 1
                interaction_type = "click"
                if "button" in line:
                    interaction_type = "button_click"
                elif "link" in line:
                    interaction_type = "link_click"

                self.report_data["component_interactions"].append({
                    "url": url,
                    "timestamp": ts,
                    "type": interaction_type,
                    "details": line_stripped
                })

            # Check for form interactions
            elif any(pattern in line for pattern in form_patterns):
                self.report_data["form_submissions"].append({
                    "url": url,
                    "timestamp": ts,
                    "type": "form_interaction",
                    "details": line_stripped
                })

            # Check for scroll interactions
            elif any(pattern in line for pattern in scroll_patterns):
                self.report_data["scroll_interactions"].append({
                    "url": url,
                    "timestamp": ts,
                    "type": "scroll_action",
                    "details": line_stripped
                })

            # Check for navigation interactions
            elif any(pattern in line for pattern in navigation_patterns):
                self.interaction_counter += 1
                self.report_data["component_interactions"].append({
                    "url": url,
                    "timestamp": ts,
                    "type": "navigation",
                    "details": line_stripped
                })

        # Add summary log entry
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': 'INFO',
            'url': url,
            'action': 'exhaustive_page_analysis',
            'details': f'Performed deep interaction analysis. Found {self.interaction_counter} interactions.',
            'parsing_summary': {
                'total_lines_processed': len(lines),
                'interactions_found': len(self.report_data["component_interactions"]),
                'forms_found': len(self.report_data["form_submissions"]),
                'scrolls_found': len(self.report_data["scroll_interactions"])
            }
        }
        self.report_data.setdefault('detailed_logs', []).append(log_entry)

    async def _ensure_browser_cleanup(self, agent: Agent):
        """Ensure the browser is properly closed."""
        try:
            self.logger.info("Ensuring browser cleanup...")
            cleanup_prompt = "MANDATORY: Call browser_close() to terminate the browser session now."
            await asyncio.wait_for(agent.arun(cleanup_prompt), timeout=10)
            self.browser_closed = True
            self.report_data["metadata"]["browser_closed"] = True
            self.logger.info("Browser cleanup command issued successfully.")
        except Exception as e:
            self.logger.warning(f"Browser cleanup may have failed or timed out: {e}")
            self.browser_closed = False
            self.report_data["metadata"]["browser_closed"] = False
            
    async def _execute_with_retry(self, agent: Agent, prompt: str, max_retries: int = None) -> str:
        """Execute agent prompt with retry logic and termination handling."""
        if max_retries is None:
            max_retries = self.config.max_retries

        for attempt in range(max_retries):
            # Check for termination before each attempt
            if self.termination_requested:
                self.logger.info("🛑 Termination requested, aborting agent execution")
                return "Execution terminated by user request"

            try:
                # Log LLM interaction if logging is available
                if self.llm_logger:
                    with self.llm_logger.log_interaction(
                        model=str(agent.model),
                        provider="browser_agent",
                        prompt=prompt,
                        metadata={
                            "agent_name": agent.name,
                            "attempt": attempt + 1,
                            "target_url": self.target_url,
                            "interaction_type": "browser_analysis"
                        }
                    ) as ctx:
                        result = await asyncio.wait_for(
                            agent.arun(prompt),
                            timeout=300  # 5 minutes for exhaustive interaction
                        )
                        response_content = result.content if hasattr(result, 'content') else str(result)
                        self.llm_logger.complete_interaction(ctx, response_content)

                        # Store LLM interaction in report
                        self._log_llm_interaction(ctx, response_content, prompt)

                        return response_content
                else:
                    # Fallback without LLM logging
                    result = await asyncio.wait_for(
                        agent.arun(prompt),
                        timeout=300  # 5 minutes for exhaustive interaction
                    )
                    return result.content if hasattr(result, 'content') else str(result)

            except asyncio.TimeoutError:
                self.logger.warning(f"⏰ Agent attempt {attempt + 1} timed out after 5 minutes")
                if self.termination_requested:
                    return "Execution terminated during timeout"
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    continue
                else:
                    return f"Operation timed out after {max_retries} attempts."
            except Exception as e:
                self.logger.error(f"Agent execution failed on attempt {attempt + 1}: {e}")
                if self.termination_requested:
                    return f"Execution terminated during error: {e}"
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                else:
                    raise e
        return "Failed to get a result from the agent after multiple retries."

    def _log_llm_interaction(self, interaction_context: Dict[str, Any], response: str, prompt: str):
        """Log LLM interaction to report data"""
        try:
            llm_interaction = {
                "interaction_id": interaction_context.get('interaction_id'),
                "timestamp": datetime.now().isoformat(),
                "model": interaction_context.get('model'),
                "provider": interaction_context.get('provider'),
                "prompt": prompt[:1000] + "..." if len(prompt) > 1000 else prompt,  # Truncate long prompts
                "response": response[:2000] + "..." if len(response) > 2000 else response,  # Truncate long responses
                "metadata": interaction_context.get('metadata', {}),
                "target_url": self.target_url
            }
            self.report_data["llm_interactions"].append(llm_interaction)

            # Update metadata counters
            self.report_data["metadata"]["llm_interactions"] = len(self.report_data["llm_interactions"])

            # Update cost if available
            if self.llm_logger:
                summary = self.llm_logger.get_session_summary()
                self.report_data["metadata"]["llm_cost_usd"] = summary.get("total_cost_usd", 0.0)

        except Exception as e:
            self.logger.warning(f"Failed to log LLM interaction: {e}")

    async def _collect_playwright_data(self, agent: Agent):
        """Collect final network and console data from Playwright MCP."""
        try:
            self.logger.info("Collecting final network and console data from Playwright MCP...")

            network_prompt = """Use browser_network_requests() to get a complete log of ALL network traffic from the session.
            I need the COMPLETE, detailed output including:
            - Full HTTP request headers and response headers
            - Request and response bodies (if available)
            - Status codes, timing information, and content types
            - All metadata available from the browser
            Provide the full, raw output exactly as returned by the tool."""
            network_result = await self._execute_with_retry(agent, network_prompt)

            console_prompt = """Use browser_console_messages() to get all console messages from the session.
            I need ALL console output including:
            - JavaScript errors with full stack traces
            - Network errors and warnings
            - Debug messages and application logs
            - Security-related console messages
            - Any other browser console output
            Provide the full, raw output exactly as returned by the tool."""
            console_result = await self._execute_with_retry(agent, console_prompt)

            recon_log = "\n".join(self.report_data["recon"])
            combined_network_data = network_result + "\n" + recon_log
            combined_console_data = console_result + "\n" + recon_log

            # Parse network data with enhanced extraction
            self.report_data["network_logs"] = self._parse_playwright_network_logs(combined_network_data)
            self.report_data["raw_request"] = self._extract_playwright_raw_requests(combined_network_data)

            # Also store the raw network and console outputs for debugging
            self.report_data["raw_network_output"] = network_result
            self.report_data["raw_console_output"] = console_result

            # Parse console data
            self.report_data["console_logs"] = self._parse_playwright_console_logs(combined_console_data)

            # Extract additional network details from the raw output
            self._extract_additional_network_details(network_result)

            self.logger.info(f"Collected {len(self.report_data['network_logs'])} network logs and {len(self.report_data['console_logs'])} console messages.")
        except Exception as e:
            self.logger.error(f"Failed to collect Playwright data: {e}")
            self.report_data.update({"network_logs": [], "console_logs": [], "raw_request": []})

    def _extract_additional_network_details(self, network_output: str):
        """Extract additional network details from raw MCP output"""
        try:
            # Look for patterns that indicate more detailed network information
            lines = network_output.split('\n')

            # Track additional network statistics
            network_stats = {
                "total_requests": 0,
                "failed_requests": 0,
                "redirects": 0,
                "cached_requests": 0,
                "request_methods": {},
                "status_codes": {},
                "content_types": {},
                "domains": set()
            }

            for line in lines:
                line = line.strip()

                # Count requests by method
                for method in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']:
                    if f'[{method}]' in line:
                        network_stats["total_requests"] += 1
                        network_stats["request_methods"][method] = network_stats["request_methods"].get(method, 0) + 1
                        break

                # Count status codes
                if '=>' in line and '[' in line and ']' in line:
                    try:
                        status_part = line.split('=>')[1].strip()
                        if '[' in status_part and ']' in status_part:
                            status_info = status_part.split('[')[1].split(']')[0]
                            status_code = status_info.split(' ')[0]
                            network_stats["status_codes"][status_code] = network_stats["status_codes"].get(status_code, 0) + 1

                            # Count failed requests (4xx, 5xx)
                            if status_code.startswith(('4', '5')):
                                network_stats["failed_requests"] += 1
                            # Count redirects (3xx)
                            elif status_code.startswith('3'):
                                network_stats["redirects"] += 1
                    except:
                        pass

                # Extract domains
                if 'http' in line:
                    import re
                    urls = re.findall(r'https?://([^/\s]+)', line)
                    for domain in urls:
                        network_stats["domains"].add(domain)

                # Look for content types
                if 'Content-Type:' in line:
                    try:
                        content_type = line.split('Content-Type:')[1].strip().split(';')[0]
                        network_stats["content_types"][content_type] = network_stats["content_types"].get(content_type, 0) + 1
                    except:
                        pass

            # Convert set to list for JSON serialization
            network_stats["domains"] = list(network_stats["domains"])

            # Add to report
            self.report_data["network_statistics"] = network_stats

            self.logger.info(f"Network Statistics: {network_stats['total_requests']} total requests, "
                           f"{network_stats['failed_requests']} failed, "
                           f"{len(network_stats['domains'])} unique domains")

        except Exception as e:
            self.logger.warning(f"Failed to extract additional network details: {e}")

    def _parse_playwright_network_logs(self, network_data: str) -> List[Dict[str, Any]]:
        """Parse network logs from Playwright MCP's raw string output with enhanced data extraction."""
        network_logs = []
        lines = network_data.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()
            if '[ID:' in line and '=>' in line:
                try:
                    parts = line.split(']', 2)
                    req_id = parts[0].replace('[ID:', '').strip()
                    method = parts[1].replace('[', '').strip()
                    url_part, status_part = parts[2].split('=>', 1)
                    url = url_part.strip()
                    status_info = status_part.strip()

                    # Extract status code and text
                    status_code = "unknown"
                    status_text = ""
                    if '[' in status_info and ']' in status_info:
                        status_match = status_info.split('[')[1].split(']')[0]
                        if ' ' in status_match:
                            status_code, status_text = status_match.split(' ', 1)
                        else:
                            status_code = status_match

                    # Parse URL components
                    parsed_url = urlparse(url)

                    # Look for additional data in subsequent lines
                    headers = {}
                    response_body = ""
                    timing_info = {}

                    # Check next few lines for headers and response data
                    for j in range(i + 1, min(i + 10, len(lines))):
                        next_line = lines[j].strip()
                        if next_line.startswith('Headers:') or 'Content-Type:' in next_line:
                            # Extract headers if present
                            if ':' in next_line:
                                header_parts = next_line.split(':', 1)
                                if len(header_parts) == 2:
                                    headers[header_parts[0].strip()] = header_parts[1].strip()
                        elif next_line.startswith('Response:') or next_line.startswith('Body:'):
                            # Extract response body if present
                            response_body = next_line.split(':', 1)[1].strip() if ':' in next_line else ""
                        elif 'ms' in next_line and ('duration' in next_line.lower() or 'time' in next_line.lower()):
                            # Extract timing information
                            timing_info['duration'] = next_line

                    network_log = {
                        "id": req_id,
                        "method": method,
                        "url": url,
                        "status_code": status_code,
                        "status_text": status_text,
                        "timestamp": datetime.now().isoformat(),
                        "source": "playwright_mcp",
                        "domain": parsed_url.netloc,
                        "path": parsed_url.path,
                        "query_params": dict(parse_qs(parsed_url.query)) if parsed_url.query else {},
                        "headers": headers,
                        "response_body": response_body[:500] + "..." if len(response_body) > 500 else response_body,
                        "timing": timing_info
                    }
                    network_logs.append(network_log)

                except (ValueError, IndexError) as e:
                    self.logger.debug(f"Failed to parse network log line: {line}, error: {e}")
                    continue

        return network_logs

    def _extract_playwright_raw_requests(self, network_data: str) -> List[Dict[str, Any]]:
        """Extract and reconstruct complete raw HTTP requests from Playwright MCP data."""
        raw_requests = []
        lines = network_data.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()
            if '[ID:' in line and '=>' in line and any(m in line for m in ['[GET]','[POST]','[PUT]','[DELETE]','[PATCH]','[HEAD]','[OPTIONS]']):
                try:
                    parts = line.split(']', 2)
                    req_id = parts[0].replace('[ID:', '').strip()
                    method = parts[1].replace('[', '').strip()
                    url_part, status_part = parts[2].split('=>', 1)
                    url = url_part.strip()
                    parsed_url = urlparse(url)
                    path = parsed_url.path or '/'
                    if parsed_url.query:
                        path += f"?{parsed_url.query}"

                    # Extract status and response info
                    status_info = status_part.strip()
                    status_code = "unknown"
                    if '[' in status_info and ']' in status_info:
                        status_match = status_info.split('[')[1].split(']')[0]
                        status_code = status_match.split(' ')[0] if ' ' in status_match else status_match

                    # Build comprehensive headers
                    headers = {
                        "Host": parsed_url.netloc,
                        "User-Agent": "Mozilla/5.0 (compatible; VAPT-Browser-Agent/1.0)",
                        "Accept": "*/*",
                        "Accept-Language": "en-US,en;q=0.9",
                        "Accept-Encoding": "gzip, deflate, br",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1"
                    }

                    # Look for additional headers and request body in subsequent lines
                    request_body = ""
                    response_headers = {}
                    response_body = ""

                    for j in range(i + 1, min(i + 15, len(lines))):
                        next_line = lines[j].strip()
                        if next_line.startswith('Request Headers:') or 'Content-Type:' in next_line:
                            if ':' in next_line:
                                header_parts = next_line.split(':', 1)
                                if len(header_parts) == 2:
                                    headers[header_parts[0].strip()] = header_parts[1].strip()
                        elif next_line.startswith('Request Body:') or next_line.startswith('POST data:'):
                            request_body = next_line.split(':', 1)[1].strip() if ':' in next_line else ""
                        elif next_line.startswith('Response Headers:'):
                            if ':' in next_line:
                                header_parts = next_line.split(':', 1)
                                if len(header_parts) == 2:
                                    response_headers[header_parts[0].strip()] = header_parts[1].strip()
                        elif next_line.startswith('Response Body:') or next_line.startswith('Response:'):
                            response_body = next_line.split(':', 1)[1].strip() if ':' in next_line else ""

                    # Add Content-Type and Content-Length for POST requests
                    if method in ['POST', 'PUT', 'PATCH'] and request_body:
                        if 'Content-Type' not in headers:
                            headers['Content-Type'] = 'application/x-www-form-urlencoded'
                        headers['Content-Length'] = str(len(request_body))

                    # Build raw HTTP request
                    raw_http_lines = [f"{method} {path} HTTP/1.1"]
                    for header_name, header_value in headers.items():
                        raw_http_lines.append(f"{header_name}: {header_value}")
                    raw_http_lines.append("")  # Empty line before body
                    if request_body:
                        raw_http_lines.append(request_body)

                    raw_http = "\r\n".join(raw_http_lines)

                    raw_request = {
                        "id": req_id,
                        "method": method,
                        "url": url,
                        "status_code": status_code,
                        "raw_request": raw_http,
                        "headers": headers,
                        "body": request_body,
                        "response_headers": response_headers,
                        "response_body": response_body[:1000] + "..." if len(response_body) > 1000 else response_body,
                        "timestamp": datetime.now().isoformat(),
                        "complete": True,
                        "source": "playwright_mcp_enhanced"
                    }
                    raw_requests.append(raw_request)

                except (ValueError, IndexError) as e:
                    self.logger.debug(f"Failed to parse raw request line: {line}, error: {e}")
                    continue

        return raw_requests
            
    def _parse_playwright_console_logs(self, console_data: str) -> List[Dict[str, Any]]:
        """Parse console logs from Playwright MCP's raw string output with enhanced parsing."""
        console_logs = []
        lines = console_data.split('\n')

        for line in lines:
            line = line.strip()
            if not line or '===' in line or line.startswith('```'):
                continue

            # Determine log level from content
            level = "info"
            if any(keyword in line.lower() for keyword in ["error", "failed", "exception", "uncaught"]):
                level = "error"
            elif any(keyword in line.lower() for keyword in ["warn", "warning", "deprecated"]):
                level = "warn"
            elif any(keyword in line.lower() for keyword in ["debug", "trace"]):
                level = "debug"
            elif any(keyword in line.lower() for keyword in ["[log]", "console.log"]):
                level = "log"

            # Extract additional information if available
            url = ""
            line_number = None
            column = None

            # Look for URL and line/column information
            if "http" in line:
                # Extract URL from the message
                import re
                url_match = re.search(r'https?://[^\s]+', line)
                if url_match:
                    url = url_match.group()

            # Look for line:column information
            line_col_match = re.search(r':(\d+):(\d+)', line)
            if line_col_match:
                line_number = int(line_col_match.group(1))
                column = int(line_col_match.group(2))

            # Clean up the message
            message = line
            if message.startswith('[LOG]'):
                message = message[5:].strip()
            elif message.startswith('[ERROR]'):
                message = message[7:].strip()
            elif message.startswith('[WARN]'):
                message = message[6:].strip()

            console_log = {
                "timestamp": datetime.now().isoformat(),
                "message": message,
                "level": level,
                "source": "playwright_mcp",
                "url": url,
                "line": line_number,
                "column": column,
                "raw_line": line
            }
            console_logs.append(console_log)

        return console_logs

    def _analyze_application_type(self, page_content: str, url: str = "") -> Dict[str, Any]:
        """Analyze application type based on page content and URL patterns."""
        analysis = {
            "detected_type": "unknown", "confidence": 0.0, "features": [],
            "technology_stack": [], "reasoning": ""
        }
        content_lower = page_content.lower()

        indicators = {
            "ecommerce": ["cart", "checkout", "product", "price", "buy", "shop"],
            "admin/dashboard": ["dashboard", "admin", "manage", "users", "settings", "analytics"],
            "cms/blog": ["post", "article", "blog", "content", "publish", "editor"],
            "social/community": ["profile", "friends", "message", "feed", "community"],
            "login_page": ["log in", "sign in", "password", "username", "login"],
        }
        scores = {key: sum(1 for ind in val if ind in content_lower) for key, val in indicators.items()}
        
        tech_indicators = {
            "react": ["react", "jsx"], "angular": ["angular", "ng-"], "vue": ["vue", "v-"],
            "jquery": ["jquery", "$."], "bootstrap": ["bootstrap"], "wordpress": ["wp-content"],
        }
        for tech, tech_inds in tech_indicators.items():
            if any(ind in content_lower for ind in tech_inds):
                analysis["technology_stack"].append(tech)

        max_score = max(scores.values()) if scores else 0
        if max_score > 0:
            detected_type = max(scores, key=scores.get)
            analysis["detected_type"] = detected_type
            analysis["confidence"] = min(max_score / 4.0, 1.0)
            analysis["reasoning"] = f"Detected {detected_type} based on {max_score} matching keywords."
        else:
            analysis["reasoning"] = "No strong indicators for a specific application type were found."

        return analysis