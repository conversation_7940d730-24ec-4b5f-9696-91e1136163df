"""
LLM Logging Utilities for VAPT Agents

Provides centralized utilities for consistent LLM interaction logging
across all VAPT agents including prompts, responses, token usage, and costs.
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

# Try to import LLM logging functionality
try:
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent / "LLM_logs"))
    from llm_logger import get_llm_logger
    LLM_LOGGING_AVAILABLE = True
except ImportError:
    LLM_LOGGING_AVAILABLE = False
    get_llm_logger = None


class AgentLLMLogger:
    """Enhanced LLM logger for VAPT agents with agent-specific context"""
    
    def __init__(self, agent_name: str, agent_type: str = "vapt_agent"):
        self.agent_name = agent_name
        self.agent_type = agent_type
        self.llm_logger = None
        self.interaction_count = 0
        
        # Initialize LLM logging if available
        if LLM_LOGGING_AVAILABLE:
            try:
                self.llm_logger = get_llm_logger()
            except Exception:
                self.llm_logger = None
    
    @contextmanager
    def log_agent_llm_interaction(self, 
                                  model: str, 
                                  provider: str, 
                                  prompt: str, 
                                  interaction_type: str = "general",
                                  target_url: str = None,
                                  additional_metadata: Optional[Dict[str, Any]] = None):
        """
        Context manager for logging agent LLM interactions with enhanced metadata
        
        Args:
            model: LLM model name
            provider: LLM provider (openai, azure_openai, etc.)
            prompt: The prompt sent to the LLM
            interaction_type: Type of interaction (scan, analysis, coordination, etc.)
            target_url: Target URL being analyzed (if applicable)
            additional_metadata: Additional metadata to include
        """
        self.interaction_count += 1
        
        # Prepare enhanced metadata
        metadata = {
            'agent_name': self.agent_name,
            'agent_type': self.agent_type,
            'interaction_type': interaction_type,
            'interaction_sequence': self.interaction_count,
            'timestamp': datetime.now().isoformat()
        }
        
        if target_url:
            metadata['target_url'] = target_url
            
        if additional_metadata:
            metadata.update(additional_metadata)
        
        # Use LLM logger if available, otherwise provide a no-op context
        if self.llm_logger:
            with self.llm_logger.log_interaction(model, provider, prompt, metadata) as ctx:
                yield ctx
        else:
            # Fallback context for when LLM logging is not available
            yield {
                'interaction_id': f"{self.agent_name}_{self.interaction_count}",
                'start_time': time.time(),
                'model': model,
                'provider': provider,
                'prompt': prompt,
                'metadata': metadata
            }
    
    def log_prompt_details(self, prompt: str, prompt_type: str = "general"):
        """Log detailed prompt information for debugging"""
        prompt_info = {
            'agent_name': self.agent_name,
            'prompt_type': prompt_type,
            'prompt_length': len(prompt),
            'prompt_lines': prompt.count('\n') + 1,
            'timestamp': datetime.now().isoformat(),
            'prompt_preview': prompt[:200] + "..." if len(prompt) > 200 else prompt
        }
        
        # Log to agent's regular logger if available
        return prompt_info
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get session summary including agent-specific metrics"""
        base_summary = {
            'agent_name': self.agent_name,
            'agent_type': self.agent_type,
            'total_interactions': self.interaction_count
        }
        
        if self.llm_logger:
            llm_summary = self.llm_logger.get_session_summary()
            base_summary.update(llm_summary)
        
        return base_summary


def create_agent_llm_logger(agent_name: str, agent_type: str = "vapt_agent") -> AgentLLMLogger:
    """Factory function to create agent LLM logger"""
    return AgentLLMLogger(agent_name, agent_type)


def log_batch_processing_details(agent_logger: AgentLLMLogger, 
                                batch_requests: List[str], 
                                target_url: str) -> Dict[str, Any]:
    """Log details about batch processing for analysis"""
    batch_info = {
        'total_requests': len(batch_requests),
        'target_url': target_url,
        'request_methods': [],
        'request_endpoints': [],
        'request_sizes': []
    }
    
    # Analyze batch requests
    for req in batch_requests:
        lines = req.split('\n')
        if lines:
            first_line = lines[0]
            parts = first_line.split(' ')
            if len(parts) >= 2:
                method = parts[0]
                endpoint = parts[1]
                batch_info['request_methods'].append(method)
                batch_info['request_endpoints'].append(endpoint)
        
        batch_info['request_sizes'].append(len(req))
    
    # Get unique methods and endpoints
    batch_info['unique_methods'] = list(set(batch_info['request_methods']))
    batch_info['unique_endpoints'] = list(set(batch_info['request_endpoints']))
    batch_info['avg_request_size'] = sum(batch_info['request_sizes']) / len(batch_info['request_sizes']) if batch_info['request_sizes'] else 0
    
    return batch_info


def format_vulnerability_results_for_logging(vulnerabilities: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Format vulnerability results for logging"""
    if not vulnerabilities:
        return {
            'vulnerability_count': 0,
            'vulnerability_types': [],
            'severity_distribution': {},
            'techniques_used': []
        }
    
    result = {
        'vulnerability_count': len(vulnerabilities),
        'vulnerability_types': [],
        'severity_distribution': {},
        'techniques_used': []
    }
    
    for vuln in vulnerabilities:
        # Collect types
        vuln_type = vuln.get('type', 'Unknown')
        result['vulnerability_types'].append(vuln_type)
        
        # Collect severity
        severity = vuln.get('severity', 'Unknown')
        result['severity_distribution'][severity] = result['severity_distribution'].get(severity, 0) + 1
        
        # Collect techniques
        technique = vuln.get('technique', 'Unknown')
        if technique != 'Unknown':
            result['techniques_used'].append(technique)
    
    # Get unique values
    result['vulnerability_types'] = list(set(result['vulnerability_types']))
    result['techniques_used'] = list(set(result['techniques_used']))
    
    return result


def log_agent_performance_metrics(agent_logger: AgentLLMLogger, 
                                 start_time: float, 
                                 end_time: float,
                                 operation_type: str,
                                 success: bool,
                                 additional_metrics: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Log performance metrics for agent operations"""
    duration = end_time - start_time
    
    metrics = {
        'agent_name': agent_logger.agent_name,
        'operation_type': operation_type,
        'duration_seconds': duration,
        'success': success,
        'timestamp': datetime.now().isoformat()
    }
    
    if additional_metrics:
        metrics.update(additional_metrics)
    
    return metrics
