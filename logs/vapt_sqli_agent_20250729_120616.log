2025-07-29 12:06:16,768 - sqli_agent - INFO - SQLi Agent initialized with PentestAI architecture
2025-07-29 12:06:16,769 - sqli_agent - INFO - SQLi Agent initialized with PentestAI architecture
2025-07-29 12:06:16,769 - sqli_agent - INFO - Successfully parsed 1 vulnerabilities from JSON response
2025-07-29 12:06:16,769 - sqli_agent - INFO - Successfully parsed 0 vulnerabilities from JSON response
2025-07-29 12:06:16,769 - sqli_agent - ERROR - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-29 12:06:16,769 - sqli_agent - INFO - SQLi Agent initialized with PentestAI architecture
2025-07-29 12:06:16,769 - sqli_agent - INFO - Starting SQLi vulnerability scan from preprocessor report
2025-07-29 12:06:16,769 - sqli_agent - INFO - Processing batch of 2 requests with single agent call
2025-07-29 12:06:16,769 - sqli_agent - INFO - Successfully parsed 0 vulnerabilities from JSON response
2025-07-29 12:06:16,770 - sqli_agent - INFO - SQLi report saved to: reports/sqli_report_example.com_20250729_120616.json
2025-07-29 12:06:16,770 - sqli_agent - INFO - SQLi scan completed. Found 0 vulnerabilities
2025-07-29 12:06:16,770 - sqli_agent - INFO - SQLi Agent initialized with PentestAI architecture
