2025-07-29 12:13:51,812 - lead_agent - INFO - Lead Agent initialized
2025-07-29 12:13:51,814 - lead_agent - INFO - All component agents initialized successfully
2025-07-29 12:13:51,814 - lead_agent - INFO - Starting vulnerability scan for types: ['sqli']
2025-07-29 12:13:51,814 - lead_agent - INFO - Running intelligent SQLi vulnerability scan
2025-07-29 12:13:51,815 - lead_agent - INFO - SQLi Target Assessment Summary:
2025-07-29 12:13:51,815 - lead_agent - INFO -   Total requests: 54
2025-07-29 12:13:51,815 - lead_agent - INFO -   Testable requests: 7
2025-07-29 12:13:51,815 - lead_agent - INFO -   High priority: 2
2025-07-29 12:13:51,815 - lead_agent - INFO -   Medium priority: 5
2025-07-29 12:13:51,815 - lead_agent - INFO -   Low priority: 0
2025-07-29 12:13:51,815 - lead_agent - INFO - Found 7 promising SQLi targets
2025-07-29 12:13:51,815 - lead_agent - INFO -   Target 1: POST https://brokencrystals.com/api/metadata
2025-07-29 12:13:51,815 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-29 12:13:51,815 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, HTTP POST method (accepts data)
2025-07-29 12:13:51,815 - lead_agent - INFO -   Target 2: POST https://brokencrystals.com/api/render
2025-07-29 12:13:51,815 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-29 12:13:51,815 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, HTTP POST method (accepts data)
2025-07-29 12:13:51,815 - lead_agent - INFO -   Target 3: GET https://brokencrystals.com/api/spawn?command=pwd
2025-07-29 12:13:51,815 - lead_agent - INFO -     Priority: 2, Confidence: 0.70
2025-07-29 12:13:51,815 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, URL contains query parameters
2025-07-29 12:13:51,815 - lead_agent - INFO -   Target 4: GET https://maps.googleapis.com/maps/api/js?client=google-maps-embed&paint_origin=&libraries=geometry,search&v=weekly&loading=async&language=en_GB&callback=onApiLoad
2025-07-29 12:13:51,815 - lead_agent - INFO -     Priority: 2, Confidence: 0.70
2025-07-29 12:13:51,815 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, URL contains query parameters
2025-07-29 12:13:51,815 - lead_agent - INFO -   Target 5: GET https://maps.googleapis.com/maps/api/mapsjs/gen_204?csp_test=true
2025-07-29 12:13:51,815 - lead_agent - INFO -     Priority: 2, Confidence: 0.70
2025-07-29 12:13:51,815 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, URL contains query parameters
2025-07-29 12:13:51,815 - lead_agent - INFO -   ... and 2 more targets
