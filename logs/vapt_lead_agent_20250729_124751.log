2025-07-29 12:47:51,358 - lead_agent - INFO - Lead Agent initialized with LLM logging
2025-07-29 12:47:51,359 - lead_agent - INFO - All component agents initialized successfully
2025-07-29 12:47:51,359 - lead_agent - INFO - Starting vulnerability scan for types: ['sqli']
2025-07-29 12:47:51,360 - lead_agent - INFO - Running intelligent SQLi vulnerability scan
2025-07-29 12:47:51,360 - lead_agent - INFO - SQLi Target Assessment Summary:
2025-07-29 12:47:51,361 - lead_agent - INFO -   Total requests: 14
2025-07-29 12:47:51,361 - lead_agent - INFO -   Testable requests: 6
2025-07-29 12:47:51,361 - lead_agent - INFO -   High priority: 4
2025-07-29 12:47:51,361 - lead_agent - INFO -   Medium priority: 2
2025-07-29 12:47:51,361 - lead_agent - INFO -   Low priority: 0
2025-07-29 12:47:51,361 - lead_agent - INFO - Found 6 promising SQLi targets
2025-07-29 12:47:51,361 - lead_agent - INFO -   Target 1: GET https://brokencrystals.com/api/users/one/undefined/adminpermission
2025-07-29 12:47:51,361 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-29 12:47:51,361 - lead_agent - INFO -     Reasons: High-risk endpoint: /api/user, High-risk endpoint: /api/users, High-risk endpoint: /user, High-risk endpoint: /admin, Client error response (may indicate input validation)
2025-07-29 12:47:51,361 - lead_agent - INFO -   Target 2: GET https://brokencrystals.com/api/users/one/undefined/photo
2025-07-29 12:47:51,361 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-29 12:47:51,361 - lead_agent - INFO -     Reasons: High-risk endpoint: /api/user, High-risk endpoint: /api/users, High-risk endpoint: /user, Client error response (may indicate input validation)
2025-07-29 12:47:51,361 - lead_agent - INFO -   Target 3: POST https://brokencrystals.com/api/metadata
2025-07-29 12:47:51,361 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-29 12:47:51,361 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, HTTP POST method (accepts data)
2025-07-29 12:47:51,361 - lead_agent - INFO -   Target 4: GET https://brokencrystals.com/api/users/one/undefined/adminpermission
2025-07-29 12:47:51,361 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-29 12:47:51,361 - lead_agent - INFO -     Reasons: High-risk endpoint: /api/user, High-risk endpoint: /api/users, High-risk endpoint: /user, High-risk endpoint: /admin, Client error response (may indicate input validation)
2025-07-29 12:47:51,361 - lead_agent - INFO -   Target 5: GET https://brokencrystals.com/api/spawn?command=pwd
2025-07-29 12:47:51,361 - lead_agent - INFO -     Priority: 2, Confidence: 0.70
2025-07-29 12:47:51,361 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, URL contains query parameters
2025-07-29 12:47:51,361 - lead_agent - INFO -   ... and 1 more targets
