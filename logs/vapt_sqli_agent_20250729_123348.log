2025-07-29 12:33:48,836 - sqli_agent - INFO - SQLi Agent initialized with PentestAI architecture and LLM logging
2025-07-29 12:33:48,838 - sqli_agent - INFO - Starting SQLi vulnerability scan from preprocessor report
2025-07-29 12:33:48,838 - sqli_agent - INFO - Batch processing details: {'total_requests': 8, 'target_url': 'https://brokencrystals.com/', 'request_methods': ['GET', 'POST', 'GET', 'GET', 'POST', 'GET', 'GET', 'GET'], 'request_endpoints': ['/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Roboto:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i', '/api/metadata', '/api/spawn?command=pwd', '/maps/embed?pb=!1m14!1m8!1m3!1d12097.433213460943!2d-74.0062269!3d40.7101282!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0xb89d1fe6bc499443!2sDowntown+Conference+Center!5e0!3m2!1smk!2sbg!4v1539943755621', '/api/render', '/maps/api/js?client=google-maps-embed&paint_origin=&libraries=geometry,search&v=weekly&loading=async&language=en_GB&callback=onApiLoad', '/maps/api/mapsjs/gen_204?csp_test=true', '/maps/api/js/StaticMapService.GetMapImage?1m2&1i1234637&2i1576828&2e1&3u14&4m2&1u563&2u384&5m6&1e0&5sen-GB&6sus&10b1&12b1&14i47083502&8e1&client=google-maps-embed&token=9755'], 'request_sizes': [207, 57, 65, 237, 55, 178, 82, 217], 'unique_methods': ['GET', 'POST'], 'unique_endpoints': ['/api/metadata', '/maps/api/mapsjs/gen_204?csp_test=true', '/maps/api/js/StaticMapService.GetMapImage?1m2&1i1234637&2i1576828&2e1&3u14&4m2&1u563&2u384&5m6&1e0&5sen-GB&6sus&10b1&12b1&14i47083502&8e1&client=google-maps-embed&token=9755', '/api/spawn?command=pwd', '/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Roboto:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i', '/maps/embed?pb=!1m14!1m8!1m3!1d12097.433213460943!2d-74.0062269!3d40.7101282!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0xb89d1fe6bc499443!2sDowntown+Conference+Center!5e0!3m2!1smk!2sbg!4v1539943755621', '/api/render', '/maps/api/js?client=google-maps-embed&paint_origin=&libraries=geometry,search&v=weekly&loading=async&language=en_GB&callback=onApiLoad'], 'avg_request_size': 137.25}
2025-07-29 12:33:48,838 - sqli_agent - INFO - Processing batch of 8 requests with single agent call
2025-07-29 12:38:55,525 - sqli_agent - INFO - Successfully parsed 0 vulnerabilities from JSON response
2025-07-29 12:38:55,525 - sqli_agent - INFO - Vulnerability analysis results: {'vulnerability_count': 0, 'vulnerability_types': [], 'severity_distribution': {}, 'techniques_used': []}
2025-07-29 12:38:55,527 - sqli_agent - INFO - SQLi report saved to: reports/sqli_report_brokencrystals.com__20250729_123855.json
2025-07-29 12:38:55,527 - sqli_agent - INFO - LLM session summary: {'agent_name': 'sqli_agent', 'agent_type': 'vulnerability_scanner', 'total_interactions': 1, 'session_id': '8ccfeb81-875f-431d-b9be-a5262d1ebc96', 'session_start': '2025-07-29T12:33:48.836073', 'total_cost_usd': 0.0022475, 'total_input_tokens': 899, 'total_output_tokens': 0, 'session_duration': '0:05:06.689657', 'average_cost_per_interaction': 0.0022475}
2025-07-29 12:38:55,527 - sqli_agent - INFO - SQLi scan performance: {'agent_name': 'sqli_agent', 'operation_type': 'sqli_scan', 'duration_seconds': 306.68878197669983, 'success': True, 'timestamp': '2025-07-29T12:38:55.527568', 'vulnerabilities_found': 0, 'batch_size': 8, 'llm_interactions': 1}
2025-07-29 12:38:55,527 - sqli_agent - INFO - SQLi scan completed. Found 0 vulnerabilities
