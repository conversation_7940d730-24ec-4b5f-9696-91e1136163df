"""
LLM Logger - Core logging functionality for LLM interactions

Provides centralized logging of LLM interactions including:
- Prompt and response tracking
- Token usage and cost calculation
- Performance metrics
- Session management
"""

import json
import time
import uuid
import threading
from datetime import datetime
from pathlib import Path
from contextlib import contextmanager
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

# Token cost estimates (per 1M tokens) - Update these based on current pricing
TOKEN_COSTS = {
    'gpt-4': {'input': 30.0, 'output': 60.0},
    'gpt-4-turbo': {'input': 10.0, 'output': 30.0},
    'gpt-3.5-turbo': {'input': 0.5, 'output': 1.5},
    'claude-3-opus': {'input': 15.0, 'output': 75.0},
    'claude-3-sonnet': {'input': 3.0, 'output': 15.0},
    'claude-3-haiku': {'input': 0.25, 'output': 1.25},
    'default': {'input': 2.5, 'output': 10.0}  # Fallback pricing
}

@dataclass
class TokenUsage:
    """Token usage information"""
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0
    
    def __post_init__(self):
        if self.total_tokens == 0:
            self.total_tokens = self.input_tokens + self.output_tokens
    
    def calculate_cost(self, model: str = 'default') -> float:
        """Calculate cost in USD based on token usage"""
        costs = TOKEN_COSTS.get(model.lower(), TOKEN_COSTS['default'])
        input_cost = (self.input_tokens / 1_000_000) * costs['input']
        output_cost = (self.output_tokens / 1_000_000) * costs['output']
        return input_cost + output_cost

@dataclass
class LLMInteraction:
    """Single LLM interaction record"""
    interaction_id: str
    timestamp: str
    model: str
    provider: str
    prompt: str
    response: str
    token_usage: TokenUsage
    cost_usd: float
    duration_seconds: float
    metadata: Dict[str, Any]

class LLMLogger:
    """Centralized LLM interaction logger"""
    
    def __init__(self, log_dir: str = "LLM_logs/logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Thread-safe logging
        self._lock = threading.Lock()
        
        # Session tracking
        self.session_id = str(uuid.uuid4())
        self.session_start = datetime.now()
        
        # Cumulative metrics
        self.total_interactions = 0
        self.total_cost = 0.0
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        
        # Create session log file
        timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
        self.session_log_file = self.log_dir / f"llm_session_{timestamp}_{self.session_id[:8]}.json"
        
        # Initialize session log
        self._write_session_header()
    
    def _write_session_header(self):
        """Write session header to log file"""
        header = {
            "session_id": self.session_id,
            "session_start": self.session_start.isoformat(),
            "interactions": []
        }
        
        with open(self.session_log_file, 'w', encoding='utf-8') as f:
            json.dump(header, f, indent=2, ensure_ascii=False)
    
    @contextmanager
    def log_interaction(self, model: str, provider: str, prompt: str, metadata: Optional[Dict[str, Any]] = None):
        """Context manager for logging LLM interactions"""
        interaction_id = str(uuid.uuid4())
        start_time = time.time()
        timestamp = datetime.now().isoformat()
        
        if metadata is None:
            metadata = {}
        
        # Yield interaction context
        interaction_context = {
            'interaction_id': interaction_id,
            'start_time': start_time,
            'model': model,
            'provider': provider,
            'prompt': prompt,
            'metadata': metadata
        }
        
        try:
            yield interaction_context
        except Exception as e:
            # Log failed interaction
            duration = time.time() - start_time
            failed_interaction = LLMInteraction(
                interaction_id=interaction_id,
                timestamp=timestamp,
                model=model,
                provider=provider,
                prompt=prompt,
                response=f"ERROR: {str(e)}",
                token_usage=TokenUsage(),
                cost_usd=0.0,
                duration_seconds=duration,
                metadata={**metadata, 'error': str(e)}
            )
            self._log_interaction(failed_interaction)
            raise
    
    def complete_interaction(self, interaction_context: Dict[str, Any], response: str, 
                           token_usage: Optional[TokenUsage] = None):
        """Complete and log an interaction"""
        duration = time.time() - interaction_context['start_time']
        
        if token_usage is None:
            # Estimate token usage if not provided
            token_usage = self._estimate_token_usage(
                interaction_context['prompt'], 
                response
            )
        
        cost = token_usage.calculate_cost()
        
        interaction = LLMInteraction(
            interaction_id=interaction_context['interaction_id'],
            timestamp=datetime.fromtimestamp(interaction_context['start_time']).isoformat(),
            model=interaction_context['model'],
            provider=interaction_context['provider'],
            prompt=interaction_context['prompt'],
            response=response,
            token_usage=token_usage,
            cost_usd=cost,
            duration_seconds=duration,
            metadata=interaction_context['metadata']
        )
        
        self._log_interaction(interaction)
    
    def _estimate_token_usage(self, prompt: str, response: str) -> TokenUsage:
        """Estimate token usage based on character count (rough approximation)"""
        # Rough estimation: ~4 characters per token for English text
        input_tokens = len(prompt) // 4
        output_tokens = len(response) // 4
        
        return TokenUsage(
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=input_tokens + output_tokens
        )
    
    def _log_interaction(self, interaction: LLMInteraction):
        """Thread-safe logging of interaction"""
        with self._lock:
            # Update cumulative metrics
            self.total_interactions += 1
            self.total_cost += interaction.cost_usd
            self.total_input_tokens += interaction.token_usage.input_tokens
            self.total_output_tokens += interaction.token_usage.output_tokens
            
            # Read current session log
            try:
                with open(self.session_log_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                session_data = {
                    "session_id": self.session_id,
                    "session_start": self.session_start.isoformat(),
                    "interactions": []
                }
            
            # Add new interaction
            session_data["interactions"].append(asdict(interaction))
            
            # Update session summary
            session_data["session_summary"] = {
                "total_interactions": self.total_interactions,
                "total_cost_usd": self.total_cost,
                "total_input_tokens": self.total_input_tokens,
                "total_output_tokens": self.total_output_tokens,
                "session_duration": str(datetime.now() - self.session_start),
                "last_updated": datetime.now().isoformat()
            }
            
            # Write updated session log
            with open(self.session_log_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get current session summary"""
        return {
            "session_id": self.session_id,
            "session_start": self.session_start.isoformat(),
            "total_interactions": self.total_interactions,
            "total_cost_usd": self.total_cost,
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "session_duration": str(datetime.now() - self.session_start),
            "average_cost_per_interaction": self.total_cost / max(self.total_interactions, 1)
        }
    
    def export_session(self, output_path: Optional[str] = None) -> str:
        """Export session data to JSON file"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"llm_session_export_{timestamp}.json"
        
        summary = self.get_session_summary()
        
        with open(self.session_log_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "session_summary": summary,
            "session_data": session_data
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return output_path

# Global logger instance
_global_logger: Optional[LLMLogger] = None
_logger_lock = threading.Lock()

def get_llm_logger() -> LLMLogger:
    """Get or create global LLM logger instance"""
    global _global_logger
    
    with _logger_lock:
        if _global_logger is None:
            _global_logger = LLMLogger()
        return _global_logger

def reset_llm_logger():
    """Reset global logger (useful for testing)"""
    global _global_logger

    with _logger_lock:
        _global_logger = None
