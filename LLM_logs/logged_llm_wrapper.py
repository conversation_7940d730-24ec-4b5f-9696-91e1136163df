"""
Logged LLM Wrapper - Wraps LLM instances with automatic logging

Provides wrapper classes that automatically log all LLM interactions
while maintaining the original interface.
"""

from typing import Any, Dict, List, Optional
from .llm_logger import get_llm_logger, TokenUsage

class LoggedLLMWrapper:
    """Base wrapper class for LLM instances with logging"""
    
    def __init__(self, llm_instance: Any, model: str, provider: str):
        self.llm_instance = llm_instance
        self.model = model
        self.provider = provider
        self.logger = get_llm_logger()
    
    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert messages list to a single prompt string"""
        if isinstance(messages, str):
            return messages
        
        prompt_parts = []
        for msg in messages:
            if isinstance(msg, dict):
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                prompt_parts.append(f"{role}: {content}")
            else:
                prompt_parts.append(str(msg))
        
        return "\n".join(prompt_parts)
    
    def _extract_response_content(self, response: Any) -> str:
        """Extract content from response object"""
        if hasattr(response, 'content'):
            return str(response.content)
        elif hasattr(response, 'text'):
            return str(response.text)
        elif hasattr(response, 'message'):
            if hasattr(response.message, 'content'):
                return str(response.message.content)
            return str(response.message)
        else:
            return str(response)
    
    def _extract_token_usage(self, response: Any) -> Optional[TokenUsage]:
        """Extract token usage from response if available"""
        try:
            if hasattr(response, 'usage'):
                usage = response.usage
                return TokenUsage(
                    input_tokens=getattr(usage, 'prompt_tokens', 0),
                    output_tokens=getattr(usage, 'completion_tokens', 0),
                    total_tokens=getattr(usage, 'total_tokens', 0)
                )
            elif hasattr(response, 'token_usage'):
                usage = response.token_usage
                return TokenUsage(
                    input_tokens=getattr(usage, 'input_tokens', 0),
                    output_tokens=getattr(usage, 'output_tokens', 0),
                    total_tokens=getattr(usage, 'total_tokens', 0)
                )
        except Exception:
            pass
        
        return None
    
    def _prepare_metadata(self, **kwargs) -> Dict[str, Any]:
        """Prepare metadata from kwargs"""
        metadata = {}
        
        # Extract common parameters
        if 'temperature' in kwargs:
            metadata['temperature'] = kwargs['temperature']
        if 'max_tokens' in kwargs:
            metadata['max_tokens'] = kwargs['max_tokens']
        if 'top_p' in kwargs:
            metadata['top_p'] = kwargs['top_p']
        
        return metadata
    
    def invoke(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """Synchronous invoke with logging"""
        # Convert messages to prompt string for logging
        prompt = self._messages_to_prompt(messages)
        metadata = self._prepare_metadata(**kwargs)
        
        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
            response = self.llm_instance.invoke(messages, **kwargs)
            
            # Extract response content
            response_content = self._extract_response_content(response)
            token_usage = self._extract_token_usage(response)
            
            self.logger.complete_interaction(ctx, response_content, token_usage)
            
            return response
    
    async def ainvoke(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """Asynchronous invoke with logging"""
        # Convert messages to prompt string for logging
        prompt = self._messages_to_prompt(messages)
        metadata = self._prepare_metadata(**kwargs)
        
        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
            response = await self.llm_instance.ainvoke(messages, **kwargs)
            
            # Extract response content
            response_content = self._extract_response_content(response)
            token_usage = self._extract_token_usage(response)
            
            self.logger.complete_interaction(ctx, response_content, token_usage)
            
            return response
    
    def __getattr__(self, name):
        """Delegate other attributes to the wrapped instance"""
        return getattr(self.llm_instance, name)

class LoggedOpenAIChat(LoggedLLMWrapper):
    """Wrapper for OpenAI Chat models"""
    
    def __init__(self, llm_instance: Any, model: str = "gpt-4"):
        super().__init__(llm_instance, model, "openai")
    
    def _extract_response_content(self, response: Any) -> str:
        """Extract content from OpenAI response"""
        try:
            if hasattr(response, 'choices') and response.choices:
                choice = response.choices[0]
                if hasattr(choice, 'message'):
                    return choice.message.content
                elif hasattr(choice, 'text'):
                    return choice.text
            return str(response)
        except Exception:
            return str(response)
    
    def _extract_token_usage(self, response: Any) -> Optional[TokenUsage]:
        """Extract token usage from OpenAI response"""
        try:
            if hasattr(response, 'usage'):
                usage = response.usage
                return TokenUsage(
                    input_tokens=usage.prompt_tokens,
                    output_tokens=usage.completion_tokens,
                    total_tokens=usage.total_tokens
                )
        except Exception:
            pass
        
        return None

class LoggedAzureOpenAI(LoggedLLMWrapper):
    """Wrapper for Azure OpenAI models"""
    
    def __init__(self, llm_instance: Any, model: str = "gpt-4"):
        super().__init__(llm_instance, model, "azure_openai")
    
    async def ainvoke(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """Asynchronous invoke with logging"""
        prompt = self._messages_to_prompt(messages)
        metadata = self._prepare_metadata(**kwargs)
        
        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
            response = await self.llm_instance.ainvoke(messages, **kwargs)
            
            response_content = self._extract_response_content(response)
            token_usage = self._extract_token_usage(response)
            
            self.logger.complete_interaction(ctx, response_content, token_usage)
            
            return response

def wrap_llm_with_logging(llm_instance: Any, model: str = "unknown", provider: str = "unknown") -> LoggedLLMWrapper:
    """
    Wrap any LLM instance with logging functionality
    
    Args:
        llm_instance: The LLM instance to wrap
        model: Model name (e.g., "gpt-4", "claude-3-sonnet")
        provider: Provider name (e.g., "openai", "anthropic")
    
    Returns:
        Wrapped LLM instance with logging
    """
    # Try to detect the type and use appropriate wrapper
    instance_type = type(llm_instance).__name__.lower()
    
    if 'openai' in instance_type and 'azure' not in instance_type:
        return LoggedOpenAIChat(llm_instance, model)
    elif 'azure' in instance_type:
        return LoggedAzureOpenAI(llm_instance, model)
    else:
        # Generic wrapper for unknown types
        class GenericLoggedLLM(LoggedLLMWrapper):
            def __init__(self, llm_instance):
                super().__init__(llm_instance, model, provider)
            
            def __getattr__(self, name):
                attr = getattr(self.llm_instance, name)
                if callable(attr) and name in ['invoke', 'ainvoke', 'run', 'arun']:
                    # Wrap callable methods with logging
                    def logged_method(*args, **kwargs):
                        prompt = str(args[0]) if args else "No prompt provided"
                        metadata = self._prepare_metadata(**kwargs)
                        
                        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
                            result = attr(*args, **kwargs)
                            response_content = str(result)
                            token_usage = self.logger._estimate_token_usage(prompt, response_content)
                            self.logger.complete_interaction(ctx, response_content, token_usage)
                            return result
                    
                    return logged_method
                return attr
        
        return GenericLoggedLLM(llm_instance)
