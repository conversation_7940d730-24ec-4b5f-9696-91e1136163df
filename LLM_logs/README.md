# LLM Logging System

A centralized logging system for tracking LLM interactions, token usage, and costs across the AI agents project.

## Features

- **Comprehensive Logging**: Tracks prompts, responses, and metadata for all LLM interactions
- **Cost Tracking**: Calculates costs based on token usage with current pricing
- **Token Usage Monitoring**: Detailed input/output token tracking
- **Performance Metrics**: Duration and throughput analysis
- **Thread-Safe**: Safe for concurrent usage across multiple agents
- **Session Management**: Groups interactions into sessions with summaries
- **Export Capabilities**: Generate detailed reports in JSON format

## Token Costs (2024)

- **Input tokens**: ~$2.50 per million
- **Output tokens**: ~$10.00 per million

Current pricing for major models:
- **GPT-4**: $30/$60 per 1M tokens (input/output)
- **GPT-4 Turbo**: $10/$30 per 1M tokens
- **GPT-3.5 Turbo**: $0.50/$1.50 per 1M tokens
- **Claude-3 Opus**: $15/$75 per 1M tokens
- **Claude-3 Sonnet**: $3/$15 per 1M tokens
- **Claude-3 Haiku**: $0.25/$1.25 per 1M tokens

## Quick Start

### Basic Usage

```python
from LLM_logs import get_llm_logger

logger = get_llm_logger()

# Log an interaction
with logger.log_interaction("gpt-4", "openai", "What is AI?") as ctx:
    response = "AI is artificial intelligence..."
    logger.complete_interaction(ctx, response)

# Get session summary
summary = logger.get_session_summary()
print(f"Total cost: ${summary['total_cost_usd']:.6f}")
```

### Automatic LLM Wrapping

```python
from LLM_logs import wrap_llm_with_logging

# Wrap any LLM instance
logged_llm = wrap_llm_with_logging(your_llm, model="gpt-4", provider="openai")

# Use normally - logging happens automatically
response = logged_llm.invoke("Hello, world!")
```

### Browser Agent Integration

The browser agent automatically logs all LLM interactions:

```python
from preprocessor.browser_agent import BrowserAgent

agent = BrowserAgent()
report_path = await agent.run_reconnaissance("https://example.com")

# LLM interactions are automatically logged and included in the report
```

## File Structure

```
LLM_logs/
├── __init__.py              # Package initialization
├── llm_logger.py           # Core logging functionality
├── logged_llm_wrapper.py   # LLM wrapper classes
├── README.md               # This file
└── logs/                   # Log files directory
    ├── llm_session_20240729_143022_a1b2c3d4.json
    └── ...
```

## Log File Format

### Session Log Structure

```json
{
  "session_id": "uuid-string",
  "session_start": "2024-07-29T14:30:22.123456",
  "interactions": [
    {
      "interaction_id": "uuid-string",
      "timestamp": "2024-07-29T14:30:25.789012",
      "model": "gpt-4",
      "provider": "openai",
      "prompt": "Analyze this webpage...",
      "response": "This webpage appears to be...",
      "token_usage": {
        "input_tokens": 150,
        "output_tokens": 300,
        "total_tokens": 450
      },
      "cost_usd": 0.0135,
      "duration_seconds": 2.5,
      "metadata": {
        "agent_name": "Browser Analyst",
        "target_url": "https://example.com",
        "temperature": 0.7
      }
    }
  ],
  "session_summary": {
    "total_interactions": 5,
    "total_cost_usd": 0.0678,
    "total_input_tokens": 750,
    "total_output_tokens": 1500,
    "session_duration": "0:05:30.123456",
    "last_updated": "2024-07-29T14:35:52.123456"
  }
}
```

## API Reference

### LLMLogger

#### Methods

- `log_interaction(model, provider, prompt, metadata=None)`: Context manager for logging interactions
- `complete_interaction(ctx, response, token_usage=None)`: Complete and log an interaction
- `get_session_summary()`: Get current session summary
- `export_session(output_path=None)`: Export session data to JSON

#### Properties

- `session_id`: Unique session identifier
- `total_interactions`: Number of interactions in session
- `total_cost`: Total cost in USD
- `total_input_tokens`: Total input tokens used
- `total_output_tokens`: Total output tokens generated

### Global Functions

- `get_llm_logger()`: Get or create global logger instance
- `reset_llm_logger()`: Reset global logger (useful for testing)
- `wrap_llm_with_logging(llm, model, provider)`: Wrap LLM with automatic logging

## Integration Examples

### Manual Logging

```python
from LLM_logs import get_llm_logger

logger = get_llm_logger()
with logger.log_interaction("model", "provider", "prompt") as ctx:
    response = "LLM response"
    logger.complete_interaction(ctx, response)
```

### Session Summary

```python
from LLM_logs import get_llm_logger

logger = get_llm_logger()
summary = logger.get_session_summary()
print(f"Total cost: ${summary['total_cost_usd']:.6f}")
```

### Browser Agent with Termination Handling

```python
from preprocessor.browser_agent import BrowserAgent

agent = BrowserAgent()  # Termination handlers automatically set up
# Press Ctrl+C during execution → graceful cleanup and emergency report
```

## Integration Status

- **LLM Factory**: All created LLMs are wrapped with logging
- **Browser Agent**: Enhanced with termination handling
- **Shared Components**: Available across all agents

## Cost Monitoring

Monitor your LLM usage costs:

```python
from LLM_logs import get_llm_logger

logger = get_llm_logger()
summary = logger.get_session_summary()

if summary['total_cost_usd'] > 1.0:  # $1 threshold
    print("⚠️  High cost session detected!")
    print(f"Current cost: ${summary['total_cost_usd']:.6f}")
```

## Thread Safety

The logging system is fully thread-safe and can be used across multiple agents and processes simultaneously. Each session maintains its own state while sharing the global logger instance.

## Error Handling

The system gracefully handles errors:
- Failed interactions are logged with error details
- Missing token usage is estimated based on text length
- File I/O errors are handled with fallback mechanisms
- Thread safety is maintained even during exceptions

## Performance

- Minimal overhead: ~1-2ms per interaction
- Efficient JSON serialization
- Thread-safe operations with minimal locking
- Automatic cleanup of old log files (configurable)

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure write access to `LLM_logs/logs/` directory
2. **Import errors**: Make sure the package is in your Python path
3. **Cost calculation**: Update token costs in `TOKEN_COSTS` dictionary

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

- Real-time cost monitoring dashboard
- Integration with external cost tracking services
- Advanced analytics and reporting
- Automatic model performance comparison
- Cost optimization recommendations
