"""
Lead Agent - The brain of the VAPT tool

This agent coordinates between different components and acts as a bridge
for routing requests to appropriate scanner agents.
"""

import json
import asyncio
import time
from typing import Dict, Any, Optional, List
from pathlib import Path
from agno.agent import Agent
from agno.team import Team

import sys
from pathlib import Path

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from shared.config import get_vapt_config, VAPTConfig
from shared.llm_factory import LLMFactory
from shared.logger import get_logger
from shared.llm_logging_utils import (
    create_agent_llm_logger,
    log_agent_performance_metrics
)
from preprocessor.browser_agent import BrowserAgent
from vapt.sqli.sqli_agent import SQLiAgent
from vapt.xss.xss_agent import XSSAgent

class LeadAgent:
    """
    Lead Agent that coordinates the entire VAPT operation
    
    Acts as the brain/bridge between different agents and handles
    routing of requests to appropriate scanner agents.
    """
    
    def __init__(self, config: Optional[VAPTConfig] = None):
        self.config = config or get_vapt_config()
        self.logger = get_logger("lead_agent", self.config.log_level)
        self.llm = LLMFactory.create_llm(self.config.llm)

        # Initialize LLM logging
        self.llm_logger = create_agent_llm_logger("lead_agent", "coordination_agent")

        # Initialize component agents
        self.browser_agent = None
        self.sqli_agent = None
        self.xss_agent = None

        self.logger.info("Lead Agent initialized with LLM logging")

    def _assess_sqli_potential(self, raw_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess the SQL injection potential of a raw request

        Args:
            raw_request: Raw request data from reconnaissance

        Returns:
            Dictionary with assessment results
        """
        assessment = {
            "sqli_potential": "none",
            "confidence": 0.0,
            "reasons": [],
            "priority": 0,
            "testable": False
        }

        method = raw_request.get("method", "").upper()
        url = raw_request.get("url", "")
        status_code = raw_request.get("status_code", "")

        # High potential indicators
        high_potential_patterns = [
            "/api/auth/login", "/api/login", "/login", "/signin",
            "/api/search", "/search", "/query",
            "/api/user", "/api/users", "/user", "/profile",
            "/api/product", "/api/products", "/product",
            "/api/order", "/api/orders", "/order",
            "/api/data", "/api/database", "/db",
            "/api/admin", "/admin",
            "/api/report", "/report"
        ]

        # Medium potential indicators
        medium_potential_patterns = [
            "/api/", "/rest/", "/graphql", "/gql",
            "id=", "user=", "search=", "query=", "filter=",
            "sort=", "order=", "limit=", "offset="
        ]

        # Low potential indicators (usually not vulnerable)
        low_potential_patterns = [
            ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg",
            ".woff", ".woff2", ".ttf", ".eot",
            "/assets/", "/static/", "/css/", "/js/", "/images/",
            "/fonts/", "/vendor/", "/lib/"
        ]

        # Check for low potential (static resources)
        if any(pattern in url.lower() for pattern in low_potential_patterns):
            assessment["sqli_potential"] = "none"
            assessment["confidence"] = 0.9
            assessment["reasons"].append("Static resource (CSS, JS, images, fonts)")
            return assessment

        # Check for high potential patterns
        high_matches = [pattern for pattern in high_potential_patterns if pattern in url.lower()]
        if high_matches:
            assessment["sqli_potential"] = "high"
            assessment["confidence"] = 0.8
            assessment["priority"] = 3
            assessment["testable"] = True
            assessment["reasons"].extend([f"High-risk endpoint: {match}" for match in high_matches])

        # Check for medium potential patterns
        medium_matches = [pattern for pattern in medium_potential_patterns if pattern in url.lower()]
        if medium_matches and assessment["sqli_potential"] == "none":
            assessment["sqli_potential"] = "medium"
            assessment["confidence"] = 0.6
            assessment["priority"] = 2
            assessment["testable"] = True
            assessment["reasons"].extend([f"API/query endpoint: {match}" for match in medium_matches])

        # Method-based assessment
        if method in ["POST", "PUT", "PATCH"]:
            if assessment["sqli_potential"] == "none":
                assessment["sqli_potential"] = "low"
                assessment["confidence"] = 0.3
                assessment["priority"] = 1
                assessment["testable"] = True
            else:
                # Boost existing assessment for POST/PUT/PATCH
                assessment["confidence"] = min(assessment["confidence"] + 0.2, 1.0)
                assessment["priority"] = min(assessment["priority"] + 1, 3)
            assessment["reasons"].append(f"HTTP {method} method (accepts data)")

        # Status code assessment
        if status_code.startswith("5"):  # 5xx errors
            assessment["confidence"] = min(assessment["confidence"] + 0.1, 1.0)
            assessment["reasons"].append("Server error response (potential vulnerability)")
        elif status_code.startswith("4"):  # 4xx errors
            if assessment["sqli_potential"] == "none":
                assessment["sqli_potential"] = "low"
                assessment["confidence"] = 0.2
                assessment["priority"] = 1
                assessment["testable"] = True
            assessment["reasons"].append("Client error response (may indicate input validation)")

        # URL parameter assessment
        if "?" in url and "=" in url:
            if assessment["sqli_potential"] == "none":
                assessment["sqli_potential"] = "medium"
                assessment["confidence"] = 0.5
                assessment["priority"] = 2
                assessment["testable"] = True
            else:
                assessment["confidence"] = min(assessment["confidence"] + 0.1, 1.0)
            assessment["reasons"].append("URL contains query parameters")

        return assessment

    def _filter_sqli_targets(self, preprocessor_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter and prioritize raw requests for SQLi testing

        Args:
            preprocessor_data: Complete preprocessor report data

        Returns:
            List of prioritized raw requests suitable for SQLi testing
        """
        raw_requests = preprocessor_data.get("raw_request", [])
        if not raw_requests:
            self.logger.warning("No raw requests found in preprocessor data")
            return []

        # Assess each request
        assessed_requests = []
        for request in raw_requests:
            assessment = self._assess_sqli_potential(request)
            if assessment["testable"]:
                request_with_assessment = request.copy()
                request_with_assessment["sqli_assessment"] = assessment
                assessed_requests.append(request_with_assessment)

        # Sort by priority (high to low) and confidence
        assessed_requests.sort(
            key=lambda x: (
                x["sqli_assessment"]["priority"],
                x["sqli_assessment"]["confidence"]
            ),
            reverse=True
        )

        # Log assessment summary
        total_requests = len(raw_requests)
        testable_requests = len(assessed_requests)
        high_priority = len([r for r in assessed_requests if r["sqli_assessment"]["priority"] == 3])
        medium_priority = len([r for r in assessed_requests if r["sqli_assessment"]["priority"] == 2])
        low_priority = len([r for r in assessed_requests if r["sqli_assessment"]["priority"] == 1])

        self.logger.info(f"SQLi Target Assessment Summary:")
        self.logger.info(f"  Total requests: {total_requests}")
        self.logger.info(f"  Testable requests: {testable_requests}")
        self.logger.info(f"  High priority: {high_priority}")
        self.logger.info(f"  Medium priority: {medium_priority}")
        self.logger.info(f"  Low priority: {low_priority}")

        return assessed_requests
    
    async def initialize_agents(self):
        """Initialize all component agents"""
        try:
            self.browser_agent = BrowserAgent(self.config)
            self.sqli_agent = SQLiAgent(self.config)
            self.xss_agent = XSSAgent(self.config)
            self.logger.info("All component agents initialized successfully")
        except Exception as e:
            self.logger.log_agent_error("lead_agent", e, "agent initialization")
            raise
    
    async def run_preprocessor_only(self, target_url: str) -> str:
        """
        Run preprocessor-only mode for reconnaissance
        
        Args:
            target_url: Target URL to analyze
            
        Returns:
            Path to generated JSON report
        """
        self.logger.info(f"Starting preprocessor-only mode for {target_url}")
        
        try:
            if not self.browser_agent:
                await self.initialize_agents()
            
            report_path = await self.browser_agent.run_reconnaissance(target_url)
            self.logger.info(f"Preprocessor completed. Report saved to: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.log_agent_error("lead_agent", e, "preprocessor-only mode")
            raise

    async def run_vulnerability_scan(self, report_path: str, vulnerability_types: List[str]) -> Dict[str, Any]:
        """
        Run vulnerability scanning using preprocessor report with comprehensive logging

        Args:
            report_path: Path to preprocessor JSON report
            vulnerability_types: List of vulnerability types to test (e.g., ['sqli', 'xss'])

        Returns:
            Dictionary containing scan results
        """
        scan_start_time = time.time()
        self.logger.info(f"Starting vulnerability scan for types: {vulnerability_types}")

        try:
            # Load preprocessor report
            with open(report_path, 'r') as f:
                preprocessor_data = json.load(f)

            target_url = preprocessor_data.get("metadata", {}).get("target_url", "unknown")

            results = {
                "target_url": target_url,
                "scan_timestamp": preprocessor_data.get("metadata", {}).get("start_time"),
                "vulnerability_results": {},
                "coordination_metrics": {
                    "vulnerability_types_requested": vulnerability_types,
                    "scan_start_time": scan_start_time
                }
            }

            # Run SQLi scanning if requested
            if 'sqli' in vulnerability_types:
                self.logger.info("Running intelligent SQLi vulnerability scan")

                # Filter and prioritize SQLi targets
                sqli_targets = self._filter_sqli_targets(preprocessor_data)

                if sqli_targets:
                    self.logger.info(f"Found {len(sqli_targets)} promising SQLi targets")

                    # Create filtered preprocessor data with only promising targets
                    filtered_preprocessor_data = preprocessor_data.copy()
                    filtered_preprocessor_data["raw_request"] = sqli_targets

                    # Log the selected targets for transparency
                    for i, target in enumerate(sqli_targets[:5]):  # Log top 5
                        assessment = target["sqli_assessment"]
                        self.logger.info(f"  Target {i+1}: {target['method']} {target['url']}")
                        self.logger.info(f"    Priority: {assessment['priority']}, Confidence: {assessment['confidence']:.2f}")
                        self.logger.info(f"    Reasons: {', '.join(assessment['reasons'])}")

                    if len(sqli_targets) > 5:
                        self.logger.info(f"  ... and {len(sqli_targets) - 5} more targets")

                    # Run SQLi scan on filtered targets
                    sqli_results = await self.sqli_agent.scan_from_report(filtered_preprocessor_data)
                    results["vulnerability_results"]["sqli"] = sqli_results
                else:
                    self.logger.warning("No promising SQLi targets found in reconnaissance data")
                    results["vulnerability_results"]["sqli"] = {
                        "status": "no_targets",
                        "message": "No requests with SQLi potential were identified",
                        "total_requests_analyzed": len(preprocessor_data.get("raw_request", [])),
                        "testable_targets": 0
                    }

            # Run XSS scanning if requested
            if 'xss' in vulnerability_types:
                self.logger.info("Running XSS vulnerability scan")
                xss_results = await self.xss_agent.scan_from_report(preprocessor_data)
                results["vulnerability_results"]["xss"] = xss_results

            # Log coordination performance metrics
            scan_end_time = time.time()
            coordination_metrics = {
                'vulnerability_types_scanned': vulnerability_types,
                'sqli_targets_found': len(results.get("vulnerability_results", {}).get("sqli", {}).get("vulnerabilities", [])),
                'xss_targets_found': len(results.get("vulnerability_results", {}).get("xss", {}).get("vulnerabilities", [])),
                'total_scan_duration': scan_end_time - scan_start_time
            }

            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, scan_start_time, scan_end_time,
                "vulnerability_coordination", True, coordination_metrics
            )
            self.logger.info(f"Lead agent coordination performance: {performance_metrics}")

            # Add coordination metrics to results
            results["coordination_metrics"].update(coordination_metrics)

            # Save combined results with LLM session summary
            results_path = self._save_scan_results(results)
            self.logger.info(f"Vulnerability scan completed. Results saved to: {results_path}")

            return results

        except Exception as e:
            scan_end_time = time.time()

            # Log error performance metrics
            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, scan_start_time, scan_end_time,
                "vulnerability_coordination", False, {'error': str(e)}
            )
            self.logger.error(f"Lead agent coordination failed. Performance: {performance_metrics}")

            self.logger.log_agent_error("lead_agent", e, "vulnerability scanning")
            raise

    async def run_full_scan(self, target_url: str, vulnerability_types: List[str] = None) -> Dict[str, Any]:
        """
        Run complete VAPT scan (preprocessor + vulnerability testing) with comprehensive logging

        Args:
            target_url: Target URL to analyze
            vulnerability_types: List of vulnerability types to test (default: all)

        Returns:
            Dictionary containing complete scan results
        """
        if vulnerability_types is None:
            vulnerability_types = ['sqli', 'xss']

        full_scan_start_time = time.time()
        self.logger.info(f"Starting full VAPT scan for {target_url}")

        try:
            # Step 1: Run preprocessor
            report_path = await self.run_preprocessor_only(target_url)

            # Step 2: Run vulnerability scans
            results = await self.run_vulnerability_scan(report_path, vulnerability_types)

            # Log full scan performance metrics
            full_scan_end_time = time.time()
            full_scan_metrics = {
                'target_url': target_url,
                'vulnerability_types': vulnerability_types,
                'total_duration': full_scan_end_time - full_scan_start_time,
                'preprocessor_completed': True,
                'vulnerability_scan_completed': True
            }

            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, full_scan_start_time, full_scan_end_time,
                "full_vapt_scan", True, full_scan_metrics
            )
            self.logger.info(f"Full VAPT scan performance: {performance_metrics}")

            self.logger.info("Full VAPT scan completed successfully")
            return results

        except Exception as e:
            full_scan_end_time = time.time()

            # Log error performance metrics
            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, full_scan_start_time, full_scan_end_time,
                "full_vapt_scan", False, {'error': str(e), 'target_url': target_url}
            )
            self.logger.error(f"Full VAPT scan failed. Performance: {performance_metrics}")

            self.logger.log_agent_error("lead_agent", e, "full scan")
            raise

    def _save_scan_results(self, results: Dict[str, Any]) -> str:
        """Save scan results to file with LLM session summary"""
        from datetime import datetime

        # Add LLM session summary to results
        llm_session_summary = self.llm_logger.get_session_summary()
        results['lead_agent_llm_session_summary'] = llm_session_summary

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        target_url = results.get("target_url", "unknown").replace("://", "_").replace("/", "_")
        filename = f"vapt_results_{target_url}_{timestamp}.json"

        # Ensure reports directory exists
        reports_dir = Path(self.config.reports_dir)
        reports_dir.mkdir(parents=True, exist_ok=True)

        results_path = reports_dir / filename

        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        self.logger.info(f"VAPT results saved to: {results_path}")
        self.logger.info(f"Lead agent LLM session summary: {llm_session_summary}")
        return str(results_path)

    def get_llm_session_summary(self) -> Dict[str, Any]:
        """Get LLM session summary for this agent"""
        return self.llm_logger.get_session_summary()
